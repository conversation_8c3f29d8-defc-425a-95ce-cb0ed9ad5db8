using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Options;
using CarDealershipManagement.Enhanced.Data;
using CarDealershipManagement.Enhanced.Models;
using CarDealershipManagement.Enhanced.Enums;
using CarDealershipManagement.Enhanced.Configuration;

namespace CarDealershipManagement.Enhanced.Services;

/// <summary>
/// واجهة خدمة المستندات
/// </summary>
public interface IDocumentService
{
    Task<CarDocument> AddCarDocumentAsync(int carId, byte[] fileData, string fileName, DocumentType documentType, string? description = null, DateTime? expiryDate = null, int? userId = null);
    Task<CustomerDocument> AddCustomerDocumentAsync(int customerId, byte[] fileData, string fileName, DocumentType documentType, string? description = null, DateTime? expiryDate = null, int? userId = null);
    Task<SupplierDocument> AddSupplierDocumentAsync(int supplierId, byte[] fileData, string fileName, DocumentType documentType, string? description = null, DateTime? expiryDate = null, int? userId = null);
    Task<SaleDocument> AddSaleDocumentAsync(int saleId, byte[] fileData, string fileName, DocumentType documentType, string? description = null, int? userId = null);
    Task<bool> DeleteDocumentAsync(int documentId, int? userId = null);
    Task<byte[]> GetDocumentDataAsync(int documentId);
    Task<IEnumerable<CarDocument>> GetCarDocumentsAsync(int carId);
    Task<IEnumerable<CustomerDocument>> GetCustomerDocumentsAsync(int customerId);
    Task<IEnumerable<SupplierDocument>> GetSupplierDocumentsAsync(int supplierId);
    Task<IEnumerable<SaleDocument>> GetSaleDocumentsAsync(int saleId);
    Task<IEnumerable<BaseDocument>> GetExpiringDocumentsAsync(int daysBeforeExpiry = 30);
    Task<bool> UpdateDocumentAsync(int documentId, string? description = null, DateTime? expiryDate = null, int? userId = null);
}

/// <summary>
/// خدمة المستندات
/// </summary>
public class DocumentService : IDocumentService
{
    private readonly CarDealershipDbContext _context;
    private readonly IFileService _fileService;
    private readonly FileOptions _fileOptions;

    public DocumentService(CarDealershipDbContext context, IFileService fileService, IOptions<FileOptions> fileOptions)
    {
        _context = context;
        _fileService = fileService;
        _fileOptions = fileOptions.Value;
    }

    public async Task<CarDocument> AddCarDocumentAsync(int carId, byte[] fileData, string fileName, DocumentType documentType, string? description = null, DateTime? expiryDate = null, int? userId = null)
    {
        // التحقق من صحة امتداد الملف
        if (!await _fileService.IsValidFileExtensionAsync(fileName, _fileOptions.AllowedDocumentExtensions))
            throw new ArgumentException("امتداد الملف غير مسموح");

        // التحقق من حجم الملف
        if (fileData.Length > _fileOptions.MaxFileSize)
            throw new ArgumentException("حجم الملف كبير جداً");

        // حفظ الملف
        var filePath = await _fileService.SaveFileAsync(fileData, fileName, Path.Combine(_fileOptions.CarFilesPath, carId.ToString()));

        var document = new CarDocument
        {
            CarId = carId,
            FileName = fileName,
            FilePath = filePath,
            FileSize = fileData.Length,
            DocumentType = documentType,
            Description = description,
            ExpiryDate = expiryDate,
            CreatedAt = DateTime.Now,
            CreatedBy = userId
        };

        _context.CarDocuments.Add(document);
        await _context.SaveChangesAsync();

        return document;
    }

    public async Task<CustomerDocument> AddCustomerDocumentAsync(int customerId, byte[] fileData, string fileName, DocumentType documentType, string? description = null, DateTime? expiryDate = null, int? userId = null)
    {
        if (!await _fileService.IsValidFileExtensionAsync(fileName, _fileOptions.AllowedDocumentExtensions))
            throw new ArgumentException("امتداد الملف غير مسموح");

        if (fileData.Length > _fileOptions.MaxFileSize)
            throw new ArgumentException("حجم الملف كبير جداً");

        var filePath = await _fileService.SaveFileAsync(fileData, fileName, Path.Combine(_fileOptions.DocumentsPath, "Customers", customerId.ToString()));

        var document = new CustomerDocument
        {
            CustomerId = customerId,
            FileName = fileName,
            FilePath = filePath,
            FileSize = fileData.Length,
            DocumentType = documentType,
            Description = description,
            ExpiryDate = expiryDate,
            CreatedAt = DateTime.Now,
            CreatedBy = userId
        };

        _context.CustomerDocuments.Add(document);
        await _context.SaveChangesAsync();

        return document;
    }

    public async Task<SupplierDocument> AddSupplierDocumentAsync(int supplierId, byte[] fileData, string fileName, DocumentType documentType, string? description = null, DateTime? expiryDate = null, int? userId = null)
    {
        if (!await _fileService.IsValidFileExtensionAsync(fileName, _fileOptions.AllowedDocumentExtensions))
            throw new ArgumentException("امتداد الملف غير مسموح");

        if (fileData.Length > _fileOptions.MaxFileSize)
            throw new ArgumentException("حجم الملف كبير جداً");

        var filePath = await _fileService.SaveFileAsync(fileData, fileName, Path.Combine(_fileOptions.DocumentsPath, "Suppliers", supplierId.ToString()));

        var document = new SupplierDocument
        {
            SupplierId = supplierId,
            FileName = fileName,
            FilePath = filePath,
            FileSize = fileData.Length,
            DocumentType = documentType,
            Description = description,
            ExpiryDate = expiryDate,
            CreatedAt = DateTime.Now,
            CreatedBy = userId
        };

        _context.SupplierDocuments.Add(document);
        await _context.SaveChangesAsync();

        return document;
    }

    public async Task<SaleDocument> AddSaleDocumentAsync(int saleId, byte[] fileData, string fileName, DocumentType documentType, string? description = null, int? userId = null)
    {
        if (!await _fileService.IsValidFileExtensionAsync(fileName, _fileOptions.AllowedDocumentExtensions))
            throw new ArgumentException("امتداد الملف غير مسموح");

        if (fileData.Length > _fileOptions.MaxFileSize)
            throw new ArgumentException("حجم الملف كبير جداً");

        var filePath = await _fileService.SaveFileAsync(fileData, fileName, Path.Combine(_fileOptions.DocumentsPath, "Sales", saleId.ToString()));

        var document = new SaleDocument
        {
            SaleId = saleId,
            FileName = fileName,
            FilePath = filePath,
            FileSize = fileData.Length,
            DocumentType = documentType,
            Description = description,
            CreatedAt = DateTime.Now,
            CreatedBy = userId
        };

        _context.SaleDocuments.Add(document);
        await _context.SaveChangesAsync();

        return document;
    }

    public async Task<bool> DeleteDocumentAsync(int documentId, int? userId = null)
    {
        // البحث في جميع أنواع المستندات
        var carDocument = await _context.CarDocuments.FindAsync(documentId);
        if (carDocument != null)
        {
            await _fileService.DeleteFileAsync(carDocument.FilePath);
            carDocument.SoftDelete(userId);
            await _context.SaveChangesAsync();
            return true;
        }

        var customerDocument = await _context.CustomerDocuments.FindAsync(documentId);
        if (customerDocument != null)
        {
            await _fileService.DeleteFileAsync(customerDocument.FilePath);
            customerDocument.SoftDelete(userId);
            await _context.SaveChangesAsync();
            return true;
        }

        var supplierDocument = await _context.SupplierDocuments.FindAsync(documentId);
        if (supplierDocument != null)
        {
            await _fileService.DeleteFileAsync(supplierDocument.FilePath);
            supplierDocument.SoftDelete(userId);
            await _context.SaveChangesAsync();
            return true;
        }

        var saleDocument = await _context.SaleDocuments.FindAsync(documentId);
        if (saleDocument != null)
        {
            await _fileService.DeleteFileAsync(saleDocument.FilePath);
            saleDocument.SoftDelete(userId);
            await _context.SaveChangesAsync();
            return true;
        }

        return false;
    }

    public async Task<byte[]> GetDocumentDataAsync(int documentId)
    {
        string? filePath = null;

        // البحث في جميع أنواع المستندات
        var carDocument = await _context.CarDocuments.FindAsync(documentId);
        if (carDocument != null && !carDocument.IsDeleted)
            filePath = carDocument.FilePath;

        var customerDocument = await _context.CustomerDocuments.FindAsync(documentId);
        if (customerDocument != null && !customerDocument.IsDeleted)
            filePath = customerDocument.FilePath;

        var supplierDocument = await _context.SupplierDocuments.FindAsync(documentId);
        if (supplierDocument != null && !supplierDocument.IsDeleted)
            filePath = supplierDocument.FilePath;

        var saleDocument = await _context.SaleDocuments.FindAsync(documentId);
        if (saleDocument != null && !saleDocument.IsDeleted)
            filePath = saleDocument.FilePath;

        if (string.IsNullOrEmpty(filePath))
            throw new FileNotFoundException("المستند غير موجود");

        return await _fileService.ReadFileAsync(filePath);
    }

    public async Task<IEnumerable<CarDocument>> GetCarDocumentsAsync(int carId)
    {
        return await _context.CarDocuments
            .Where(d => d.CarId == carId && !d.IsDeleted)
            .OrderByDescending(d => d.CreatedAt)
            .ToListAsync();
    }

    public async Task<IEnumerable<CustomerDocument>> GetCustomerDocumentsAsync(int customerId)
    {
        return await _context.CustomerDocuments
            .Where(d => d.CustomerId == customerId && !d.IsDeleted)
            .OrderByDescending(d => d.CreatedAt)
            .ToListAsync();
    }

    public async Task<IEnumerable<SupplierDocument>> GetSupplierDocumentsAsync(int supplierId)
    {
        return await _context.SupplierDocuments
            .Where(d => d.SupplierId == supplierId && !d.IsDeleted)
            .OrderByDescending(d => d.CreatedAt)
            .ToListAsync();
    }

    public async Task<IEnumerable<SaleDocument>> GetSaleDocumentsAsync(int saleId)
    {
        return await _context.SaleDocuments
            .Where(d => d.SaleId == saleId && !d.IsDeleted)
            .OrderByDescending(d => d.CreatedAt)
            .ToListAsync();
    }

    public async Task<IEnumerable<BaseDocument>> GetExpiringDocumentsAsync(int daysBeforeExpiry = 30)
    {
        var expiryDate = DateTime.Today.AddDays(daysBeforeExpiry);
        var documents = new List<BaseDocument>();

        var carDocuments = await _context.CarDocuments
            .Where(d => d.ExpiryDate.HasValue && d.ExpiryDate <= expiryDate && !d.IsDeleted)
            .Cast<BaseDocument>()
            .ToListAsync();

        var customerDocuments = await _context.CustomerDocuments
            .Where(d => d.ExpiryDate.HasValue && d.ExpiryDate <= expiryDate && !d.IsDeleted)
            .Cast<BaseDocument>()
            .ToListAsync();

        var supplierDocuments = await _context.SupplierDocuments
            .Where(d => d.ExpiryDate.HasValue && d.ExpiryDate <= expiryDate && !d.IsDeleted)
            .Cast<BaseDocument>()
            .ToListAsync();

        documents.AddRange(carDocuments);
        documents.AddRange(customerDocuments);
        documents.AddRange(supplierDocuments);

        return documents.OrderBy(d => d.ExpiryDate);
    }

    public async Task<bool> UpdateDocumentAsync(int documentId, string? description = null, DateTime? expiryDate = null, int? userId = null)
    {
        // البحث في جميع أنواع المستندات وتحديثها
        var carDocument = await _context.CarDocuments.FindAsync(documentId);
        if (carDocument != null && !carDocument.IsDeleted)
        {
            if (description != null) carDocument.Description = description;
            if (expiryDate.HasValue) carDocument.ExpiryDate = expiryDate;
            carDocument.UpdateModificationInfo(userId);
            await _context.SaveChangesAsync();
            return true;
        }

        var customerDocument = await _context.CustomerDocuments.FindAsync(documentId);
        if (customerDocument != null && !customerDocument.IsDeleted)
        {
            if (description != null) customerDocument.Description = description;
            if (expiryDate.HasValue) customerDocument.ExpiryDate = expiryDate;
            customerDocument.UpdateModificationInfo(userId);
            await _context.SaveChangesAsync();
            return true;
        }

        var supplierDocument = await _context.SupplierDocuments.FindAsync(documentId);
        if (supplierDocument != null && !supplierDocument.IsDeleted)
        {
            if (description != null) supplierDocument.Description = description;
            if (expiryDate.HasValue) supplierDocument.ExpiryDate = expiryDate;
            supplierDocument.UpdateModificationInfo(userId);
            await _context.SaveChangesAsync();
            return true;
        }

        var saleDocument = await _context.SaleDocuments.FindAsync(documentId);
        if (saleDocument != null && !saleDocument.IsDeleted)
        {
            if (description != null) saleDocument.Description = description;
            saleDocument.UpdateModificationInfo(userId);
            await _context.SaveChangesAsync();
            return true;
        }

        return false;
    }
}
