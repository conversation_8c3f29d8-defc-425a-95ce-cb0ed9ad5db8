using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Options;
using BCrypt.Net;
using CarDealershipManagement.Enhanced.Data;
using CarDealershipManagement.Enhanced.Models;
using CarDealershipManagement.Enhanced.Enums;
using CarDealershipManagement.Enhanced.Configuration;
using System.Security.Cryptography;
using System.Text;

namespace CarDealershipManagement.Enhanced.Services;

/// <summary>
/// واجهة خدمة الأمان
/// </summary>
public interface ISecurityService
{
    Task<string> HashPasswordAsync(string password);
    Task<bool> VerifyPasswordAsync(string password, string hash);
    Task<string> GenerateSecureTokenAsync();
    Task<string> EncryptDataAsync(string data);
    Task<string> DecryptDataAsync(string encryptedData);
    Task<bool> ValidatePasswordStrengthAsync(string password);
    Task<string> GenerateRandomPasswordAsync(int length = 12);
    Task<bool> IsAccountLockedAsync(int userId);
    Task<bool> ShouldLockAccountAsync(int userId);
    Task LogSecurityEventAsync(string eventType, string description, int? userId = null);
}

/// <summary>
/// خدمة الأمان
/// </summary>
public class SecurityService : ISecurityService
{
    private readonly CarDealershipDbContext _context;
    private readonly SecurityOptions _securityOptions;

    public SecurityService(CarDealershipDbContext context, IOptions<SecurityOptions> securityOptions)
    {
        _context = context;
        _securityOptions = securityOptions.Value;
    }

    public async Task<string> HashPasswordAsync(string password)
    {
        return await Task.Run(() => BCrypt.Net.BCrypt.HashPassword(password, 12));
    }

    public async Task<bool> VerifyPasswordAsync(string password, string hash)
    {
        return await Task.Run(() => BCrypt.Net.BCrypt.Verify(password, hash));
    }

    public async Task<string> GenerateSecureTokenAsync()
    {
        return await Task.Run(() =>
        {
            using var rng = RandomNumberGenerator.Create();
            var bytes = new byte[32];
            rng.GetBytes(bytes);
            return Convert.ToBase64String(bytes);
        });
    }

    public async Task<string> EncryptDataAsync(string data)
    {
        if (string.IsNullOrEmpty(_securityOptions.EncryptionKey))
            return data; // إذا لم يتم تعيين مفتاح التشفير، إرجاع البيانات كما هي

        return await Task.Run(() =>
        {
            using var aes = Aes.Create();
            aes.Key = Encoding.UTF8.GetBytes(_securityOptions.EncryptionKey.PadRight(32).Substring(0, 32));
            aes.GenerateIV();

            using var encryptor = aes.CreateEncryptor();
            var dataBytes = Encoding.UTF8.GetBytes(data);
            var encryptedBytes = encryptor.TransformFinalBlock(dataBytes, 0, dataBytes.Length);

            var result = new byte[aes.IV.Length + encryptedBytes.Length];
            Array.Copy(aes.IV, 0, result, 0, aes.IV.Length);
            Array.Copy(encryptedBytes, 0, result, aes.IV.Length, encryptedBytes.Length);

            return Convert.ToBase64String(result);
        });
    }

    public async Task<string> DecryptDataAsync(string encryptedData)
    {
        if (string.IsNullOrEmpty(_securityOptions.EncryptionKey))
            return encryptedData; // إذا لم يتم تعيين مفتاح التشفير، إرجاع البيانات كما هي

        return await Task.Run(() =>
        {
            var dataBytes = Convert.FromBase64String(encryptedData);

            using var aes = Aes.Create();
            aes.Key = Encoding.UTF8.GetBytes(_securityOptions.EncryptionKey.PadRight(32).Substring(0, 32));

            var iv = new byte[16];
            var encryptedBytes = new byte[dataBytes.Length - 16];
            Array.Copy(dataBytes, 0, iv, 0, 16);
            Array.Copy(dataBytes, 16, encryptedBytes, 0, encryptedBytes.Length);

            aes.IV = iv;

            using var decryptor = aes.CreateDecryptor();
            var decryptedBytes = decryptor.TransformFinalBlock(encryptedBytes, 0, encryptedBytes.Length);

            return Encoding.UTF8.GetString(decryptedBytes);
        });
    }

    public async Task<bool> ValidatePasswordStrengthAsync(string password)
    {
        return await Task.Run(() =>
        {
            if (password.Length < _securityOptions.PasswordMinLength)
                return false;

            var hasUpper = password.Any(char.IsUpper);
            var hasLower = password.Any(char.IsLower);
            var hasDigit = password.Any(char.IsDigit);
            var hasSpecial = password.Any(c => !char.IsLetterOrDigit(c));

            return hasUpper && hasLower && hasDigit && hasSpecial;
        });
    }

    public async Task<string> GenerateRandomPasswordAsync(int length = 12)
    {
        return await Task.Run(() =>
        {
            const string upperCase = "ABCDEFGHIJKLMNOPQRSTUVWXYZ";
            const string lowerCase = "abcdefghijklmnopqrstuvwxyz";
            const string digits = "0123456789";
            const string specialChars = "!@#$%^&*()";

            using var rng = RandomNumberGenerator.Create();
            var password = new StringBuilder();

            // ضمان وجود حرف واحد على الأقل من كل نوع
            password.Append(GetRandomChar(upperCase, rng));
            password.Append(GetRandomChar(lowerCase, rng));
            password.Append(GetRandomChar(digits, rng));
            password.Append(GetRandomChar(specialChars, rng));

            // ملء باقي الطول
            var allChars = upperCase + lowerCase + digits + specialChars;
            for (int i = 4; i < length; i++)
            {
                password.Append(GetRandomChar(allChars, rng));
            }

            // خلط الأحرف
            return ShuffleString(password.ToString(), rng);
        });
    }

    public async Task<bool> IsAccountLockedAsync(int userId)
    {
        var user = await _context.Users.FindAsync(userId);
        if (user == null)
            return false;

        if (!user.IsLocked)
            return false;

        // التحقق من انتهاء فترة القفل
        if (user.LockedDate.HasValue)
        {
            var lockoutEnd = user.LockedDate.Value.AddMinutes(_securityOptions.LockoutDurationMinutes);
            if (DateTime.Now > lockoutEnd)
            {
                // إلغاء القفل تلقائياً
                user.IsLocked = false;
                user.LockedDate = null;
                user.LoginAttempts = 0;
                await _context.SaveChangesAsync();
                return false;
            }
        }

        return true;
    }

    public async Task<bool> ShouldLockAccountAsync(int userId)
    {
        var user = await _context.Users.FindAsync(userId);
        if (user == null)
            return false;

        return user.LoginAttempts >= _securityOptions.MaxLoginAttempts;
    }

    public async Task LogSecurityEventAsync(string eventType, string description, int? userId = null)
    {
        var auditLog = new AuditLog
        {
            TableName = "Security",
            RecordId = userId ?? 0,
            Action = AuditAction.Security,
            OldValues = eventType,
            NewValues = description,
            UserId = userId,
            Timestamp = DateTime.Now
        };

        _context.AuditLogs.Add(auditLog);
        await _context.SaveChangesAsync();
    }

    private static char GetRandomChar(string chars, RandomNumberGenerator rng)
    {
        var bytes = new byte[4];
        rng.GetBytes(bytes);
        var value = BitConverter.ToUInt32(bytes, 0);
        return chars[(int)(value % chars.Length)];
    }

    private static string ShuffleString(string input, RandomNumberGenerator rng)
    {
        var chars = input.ToCharArray();
        for (int i = chars.Length - 1; i > 0; i--)
        {
            var bytes = new byte[4];
            rng.GetBytes(bytes);
            var j = (int)(BitConverter.ToUInt32(bytes, 0) % (i + 1));
            (chars[i], chars[j]) = (chars[j], chars[i]);
        }
        return new string(chars);
    }
}

/// <summary>
/// واجهة خدمة الصلاحيات
/// </summary>
public interface IPermissionService
{
    Task<bool> HasPermissionAsync(int userId, string permissionName);
    Task<IEnumerable<string>> GetUserPermissionsAsync(int userId);
    Task<bool> GrantPermissionAsync(int userId, string permissionName, int grantedBy);
    Task<bool> RevokePermissionAsync(int userId, string permissionName, int revokedBy);
    Task<IEnumerable<string>> GetAllPermissionsAsync();
    Task<bool> IsPermissionValidAsync(string permissionName);
    Task<IEnumerable<UserPermission>> GetPermissionsByRoleAsync(UserRole role);
}

/// <summary>
/// خدمة الصلاحيات
/// </summary>
public class PermissionService : IPermissionService
{
    private readonly CarDealershipDbContext _context;

    // قائمة جميع الصلاحيات المتاحة في النظام
    private static readonly Dictionary<string, string> AllPermissions = new()
    {
        // صلاحيات إدارة المستخدمين
        { "Users.View", "عرض المستخدمين" },
        { "Users.Add", "إضافة مستخدم جديد" },
        { "Users.Edit", "تعديل بيانات المستخدم" },
        { "Users.Delete", "حذف المستخدم" },
        { "Users.ManagePermissions", "إدارة صلاحيات المستخدمين" },
        { "Users.ResetPassword", "إعادة تعيين كلمة المرور" },
        { "Users.LockUnlock", "قفل وإلغاء قفل المستخدمين" },

        // صلاحيات إدارة السيارات
        { "Cars.View", "عرض السيارات" },
        { "Cars.Add", "إضافة سيارة جديدة" },
        { "Cars.Edit", "تعديل بيانات السيارة" },
        { "Cars.Delete", "حذف السيارة" },
        { "Cars.ManageImages", "إدارة صور السيارات" },
        { "Cars.ManageDocuments", "إدارة مستندات السيارات" },
        { "Cars.ViewPrices", "عرض أسعار السيارات" },
        { "Cars.EditPrices", "تعديل أسعار السيارات" },

        // صلاحيات إدارة العملاء
        { "Customers.View", "عرض العملاء" },
        { "Customers.Add", "إضافة عميل جديد" },
        { "Customers.Edit", "تعديل بيانات العميل" },
        { "Customers.Delete", "حذف العميل" },
        { "Customers.ManageDocuments", "إدارة مستندات العملاء" },
        { "Customers.ViewFinancial", "عرض البيانات المالية للعملاء" },
        { "Customers.ManageCreditLimit", "إدارة الحد الائتماني" },

        // صلاحيات إدارة الموردين
        { "Suppliers.View", "عرض الموردين" },
        { "Suppliers.Add", "إضافة مورد جديد" },
        { "Suppliers.Edit", "تعديل بيانات المورد" },
        { "Suppliers.Delete", "حذف المورد" },
        { "Suppliers.ManageDocuments", "إدارة مستندات الموردين" },
        { "Suppliers.ManagePayments", "إدارة مدفوعات الموردين" },

        // صلاحيات إدارة المبيعات
        { "Sales.View", "عرض المبيعات" },
        { "Sales.Add", "إضافة بيع جديد" },
        { "Sales.Edit", "تعديل بيانات البيع" },
        { "Sales.Delete", "حذف البيع" },
        { "Sales.Confirm", "تأكيد البيع" },
        { "Sales.Cancel", "إلغاء البيع" },
        { "Sales.ViewCommissions", "عرض العمولات" },

        // صلاحيات إدارة الأقساط
        { "Installments.View", "عرض الأقساط" },
        { "Installments.Add", "إضافة قسط جديد" },
        { "Installments.Edit", "تعديل بيانات القسط" },
        { "Installments.Delete", "حذف القسط" },
        { "Installments.RecordPayment", "تسجيل دفع القسط" },
        { "Installments.Postpone", "تأجيل القسط" },
        { "Installments.ApplyLateFee", "تطبيق غرامة التأخير" },

        // صلاحيات التقارير
        { "Reports.View", "عرض التقارير" },
        { "Reports.Generate", "إنشاء التقارير" },
        { "Reports.Export", "تصدير التقارير" },
        { "Reports.Financial", "التقارير المالية" },
        { "Reports.Sales", "تقارير المبيعات" },
        { "Reports.Inventory", "تقارير المخزون" },

        // صلاحيات النظام
        { "System.Backup", "النسخ الاحتياطية" },
        { "System.Restore", "استعادة النسخ الاحتياطية" },
        { "System.Settings", "إعدادات النظام" },
        { "System.Logs", "عرض سجلات النظام" },
        { "System.Maintenance", "صيانة النظام" },

        // صلاحيات الأرشفة
        { "Archive.View", "عرض الأرشيف" },
        { "Archive.Add", "إضافة للأرشيف" },
        { "Archive.Restore", "استعادة من الأرشيف" },
        { "Archive.Delete", "حذف من الأرشيف نهائياً" }
    };

    public PermissionService(CarDealershipDbContext context)
    {
        _context = context;
    }

    public async Task<bool> HasPermissionAsync(int userId, string permissionName)
    {
        return await _context.UserPermissions
            .AnyAsync(p => p.UserId == userId && 
                          p.PermissionName == permissionName && 
                          p.IsActive &&
                          (!p.ExpiryDate.HasValue || p.ExpiryDate > DateTime.Now));
    }

    public async Task<IEnumerable<string>> GetUserPermissionsAsync(int userId)
    {
        return await _context.UserPermissions
            .Where(p => p.UserId == userId && 
                       p.IsActive &&
                       (!p.ExpiryDate.HasValue || p.ExpiryDate > DateTime.Now))
            .Select(p => p.PermissionName)
            .ToListAsync();
    }

    public async Task<bool> GrantPermissionAsync(int userId, string permissionName, int grantedBy)
    {
        if (!await IsPermissionValidAsync(permissionName))
            return false;

        // التحقق من عدم وجود الصلاحية مسبقاً
        var existingPermission = await _context.UserPermissions
            .FirstOrDefaultAsync(p => p.UserId == userId && p.PermissionName == permissionName);

        if (existingPermission != null)
        {
            if (!existingPermission.IsActive)
            {
                existingPermission.IsActive = true;
                existingPermission.UpdateModificationInfo(grantedBy);
            }
        }
        else
        {
            var permission = new UserPermission
            {
                UserId = userId,
                PermissionName = permissionName,
                Description = AllPermissions.GetValueOrDefault(permissionName, permissionName),
                IsActive = true,
                CreatedAt = DateTime.Now,
                CreatedBy = grantedBy
            };

            _context.UserPermissions.Add(permission);
        }

        await _context.SaveChangesAsync();
        return true;
    }

    public async Task<bool> RevokePermissionAsync(int userId, string permissionName, int revokedBy)
    {
        var permission = await _context.UserPermissions
            .FirstOrDefaultAsync(p => p.UserId == userId && p.PermissionName == permissionName);

        if (permission == null)
            return false;

        permission.IsActive = false;
        permission.UpdateModificationInfo(revokedBy);

        await _context.SaveChangesAsync();
        return true;
    }

    public async Task<IEnumerable<string>> GetAllPermissionsAsync()
    {
        return await Task.FromResult(AllPermissions.Keys);
    }

    public async Task<bool> IsPermissionValidAsync(string permissionName)
    {
        return await Task.FromResult(AllPermissions.ContainsKey(permissionName));
    }

    public async Task<IEnumerable<UserPermission>> GetPermissionsByRoleAsync(UserRole role)
    {
        var userIds = await _context.Users
            .Where(u => u.Role == role)
            .Select(u => u.Id)
            .ToListAsync();

        return await _context.UserPermissions
            .Where(p => userIds.Contains(p.UserId) && p.IsActive)
            .ToListAsync();
    }
}
