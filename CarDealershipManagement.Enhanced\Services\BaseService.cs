using Microsoft.EntityFrameworkCore;
using CarDealershipManagement.Enhanced.Data;
using CarDealershipManagement.Enhanced.Models;
using CarDealershipManagement.Enhanced.Services.Interfaces;
using System.Linq.Expressions;

namespace CarDealershipManagement.Enhanced.Services;

/// <summary>
/// الخدمة الأساسية للعمليات العامة
/// </summary>
/// <typeparam name="T">نوع الكيان</typeparam>
public abstract class BaseService<T> : IBaseService<T> where T : BaseEntity
{
    protected readonly CarDealershipDbContext _context;
    protected readonly DbSet<T> _dbSet;

    protected BaseService(CarDealershipDbContext context)
    {
        _context = context;
        _dbSet = context.Set<T>();
    }

    /// <summary>
    /// الحصول على جميع السجلات
    /// </summary>
    public virtual async Task<IEnumerable<T>> GetAllAsync(bool includeDeleted = false)
    {
        var query = _dbSet.AsQueryable();
        
        if (includeDeleted)
        {
            query = query.IgnoreQueryFilters();
        }

        return await query.ToListAsync();
    }

    /// <summary>
    /// الحصول على سجل بالمعرف
    /// </summary>
    public virtual async Task<T?> GetByIdAsync(int id, bool includeDeleted = false)
    {
        var query = _dbSet.AsQueryable();
        
        if (includeDeleted)
        {
            query = query.IgnoreQueryFilters();
        }

        return await query.FirstOrDefaultAsync(e => e.Id == id);
    }

    /// <summary>
    /// البحث في السجلات
    /// </summary>
    public virtual async Task<IEnumerable<T>> FindAsync(Expression<Func<T, bool>> predicate, bool includeDeleted = false)
    {
        var query = _dbSet.AsQueryable();
        
        if (includeDeleted)
        {
            query = query.IgnoreQueryFilters();
        }

        return await query.Where(predicate).ToListAsync();
    }

    /// <summary>
    /// الحصول على أول سجل مطابق
    /// </summary>
    public virtual async Task<T?> FirstOrDefaultAsync(Expression<Func<T, bool>> predicate, bool includeDeleted = false)
    {
        var query = _dbSet.AsQueryable();
        
        if (includeDeleted)
        {
            query = query.IgnoreQueryFilters();
        }

        return await query.FirstOrDefaultAsync(predicate);
    }

    /// <summary>
    /// إضافة سجل جديد
    /// </summary>
    public virtual async Task<T> AddAsync(T entity, int? userId = null)
    {
        if (!await ValidateEntityAsync(entity))
            throw new InvalidOperationException("فشل في التحقق من صحة البيانات");

        entity.CreatedAt = DateTime.Now;
        entity.CreatedBy = userId;

        await _dbSet.AddAsync(entity);
        await SaveChangesAsync();

        await OnAfterAddAsync(entity, userId);

        return entity;
    }

    /// <summary>
    /// إضافة عدة سجلات
    /// </summary>
    public virtual async Task<IEnumerable<T>> AddRangeAsync(IEnumerable<T> entities, int? userId = null)
    {
        var entityList = entities.ToList();
        var now = DateTime.Now;
        
        foreach (var entity in entityList)
        {
            entity.CreatedAt = now;
            entity.CreatedBy = userId;
        }
        
        await _dbSet.AddRangeAsync(entityList);
        await SaveChangesAsync();
        
        return entityList;
    }

    /// <summary>
    /// تحديث سجل
    /// </summary>
    public virtual async Task<T> UpdateAsync(T entity, int? userId = null)
    {
        if (!await ValidateEntityAsync(entity))
            throw new InvalidOperationException("فشل في التحقق من صحة البيانات");

        entity.UpdateModificationInfo(userId);

        _dbSet.Update(entity);
        await SaveChangesAsync();

        await OnAfterUpdateAsync(entity, userId);

        return entity;
    }

    /// <summary>
    /// تحديث عدة سجلات
    /// </summary>
    public virtual async Task<IEnumerable<T>> UpdateRangeAsync(IEnumerable<T> entities, int? userId = null)
    {
        var entityList = entities.ToList();
        
        foreach (var entity in entityList)
        {
            entity.UpdateModificationInfo(userId);
        }
        
        _dbSet.UpdateRange(entityList);
        await SaveChangesAsync();
        
        return entityList;
    }

    /// <summary>
    /// حذف سجل (حذف ناعم)
    /// </summary>
    public virtual async Task<bool> DeleteAsync(int id, int? userId = null)
    {
        var entity = await GetByIdAsync(id);
        if (entity == null)
            return false;

        return await DeleteAsync(entity, userId);
    }

    /// <summary>
    /// حذف سجل (حذف ناعم)
    /// </summary>
    public virtual async Task<bool> DeleteAsync(T entity, int? userId = null)
    {
        entity.SoftDelete(userId);

        _dbSet.Update(entity);
        await SaveChangesAsync();

        await OnAfterDeleteAsync(entity, userId);

        return true;
    }

    /// <summary>
    /// حذف عدة سجلات (حذف ناعم)
    /// </summary>
    public virtual async Task<int> DeleteRangeAsync(IEnumerable<int> ids, int? userId = null)
    {
        var entities = await _dbSet.Where(e => ids.Contains(e.Id)).ToListAsync();
        
        foreach (var entity in entities)
        {
            entity.SoftDelete(userId);
        }
        
        _dbSet.UpdateRange(entities);
        await SaveChangesAsync();
        
        return entities.Count;
    }

    /// <summary>
    /// استعادة سجل محذوف
    /// </summary>
    public virtual async Task<bool> RestoreAsync(int id, int? userId = null)
    {
        var entity = await _dbSet.IgnoreQueryFilters()
            .FirstOrDefaultAsync(e => e.Id == id && e.IsDeleted);
        
        if (entity == null)
            return false;

        entity.Restore(userId);
        
        _dbSet.Update(entity);
        await SaveChangesAsync();
        
        return true;
    }

    /// <summary>
    /// حذف سجل نهائياً
    /// </summary>
    public virtual async Task<bool> PermanentDeleteAsync(int id)
    {
        var entity = await _dbSet.IgnoreQueryFilters()
            .FirstOrDefaultAsync(e => e.Id == id);
        
        if (entity == null)
            return false;

        _dbSet.Remove(entity);
        await SaveChangesAsync();
        
        return true;
    }

    /// <summary>
    /// التحقق من وجود سجل
    /// </summary>
    public virtual async Task<bool> ExistsAsync(Expression<Func<T, bool>> predicate, bool includeDeleted = false)
    {
        var query = _dbSet.AsQueryable();
        
        if (includeDeleted)
        {
            query = query.IgnoreQueryFilters();
        }

        return await query.AnyAsync(predicate);
    }

    /// <summary>
    /// عدد السجلات
    /// </summary>
    public virtual async Task<int> CountAsync(Expression<Func<T, bool>>? predicate = null, bool includeDeleted = false)
    {
        var query = _dbSet.AsQueryable();
        
        if (includeDeleted)
        {
            query = query.IgnoreQueryFilters();
        }

        if (predicate != null)
        {
            query = query.Where(predicate);
        }

        return await query.CountAsync();
    }

    /// <summary>
    /// الحصول على السجلات مع الترقيم
    /// </summary>
    public virtual async Task<PagedResult<T>> GetPagedAsync(
        int pageNumber,
        int pageSize,
        Expression<Func<T, bool>>? predicate = null,
        Func<IQueryable<T>, IOrderedQueryable<T>>? orderBy = null,
        bool includeDeleted = false)
    {
        var query = _dbSet.AsQueryable();
        
        if (includeDeleted)
        {
            query = query.IgnoreQueryFilters();
        }

        if (predicate != null)
        {
            query = query.Where(predicate);
        }

        var totalCount = await query.CountAsync();

        if (orderBy != null)
        {
            query = orderBy(query);
        }

        var data = await query
            .Skip((pageNumber - 1) * pageSize)
            .Take(pageSize)
            .ToListAsync();

        return new PagedResult<T>
        {
            Data = data,
            TotalCount = totalCount,
            PageNumber = pageNumber,
            PageSize = pageSize
        };
    }

    /// <summary>
    /// حفظ التغييرات
    /// </summary>
    public virtual async Task<int> SaveChangesAsync()
    {
        return await _context.SaveChangesAsync();
    }

    /// <summary>
    /// تطبيق الفلاتر المخصصة (يمكن للخدمات المشتقة تخصيصها)
    /// </summary>
    protected virtual IQueryable<T> ApplyIncludes(IQueryable<T> query)
    {
        return query;
    }

    /// <summary>
    /// تطبيق الترتيب الافتراضي (يمكن للخدمات المشتقة تخصيصها)
    /// </summary>
    protected virtual IQueryable<T> ApplyDefaultOrdering(IQueryable<T> query)
    {
        return query.OrderByDescending(e => e.CreatedAt);
    }

    /// <summary>
    /// التحقق من صحة البيانات قبل الحفظ (يمكن للخدمات المشتقة تخصيصها)
    /// </summary>
    protected virtual async Task<bool> ValidateEntityAsync(T entity)
    {
        return await Task.FromResult(true);
    }

    /// <summary>
    /// معالجة ما بعد الإضافة (يمكن للخدمات المشتقة تخصيصها)
    /// </summary>
    protected virtual async Task OnAfterAddAsync(T entity, int? userId = null)
    {
        await Task.CompletedTask;
    }

    /// <summary>
    /// معالجة ما بعد التحديث (يمكن للخدمات المشتقة تخصيصها)
    /// </summary>
    protected virtual async Task OnAfterUpdateAsync(T entity, int? userId = null)
    {
        await Task.CompletedTask;
    }

    /// <summary>
    /// معالجة ما بعد الحذف (يمكن للخدمات المشتقة تخصيصها)
    /// </summary>
    protected virtual async Task OnAfterDeleteAsync(T entity, int? userId = null)
    {
        await Task.CompletedTask;
    }
}
