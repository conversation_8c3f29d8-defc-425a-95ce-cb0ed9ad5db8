using System.ComponentModel.DataAnnotations;

namespace CarDealershipManagement.Enhanced.Models;

/// <summary>
/// الكلاس الأساسي لجميع الكيانات في النظام
/// </summary>
public abstract class BaseEntity
{
    /// <summary>
    /// المعرف الفريد للكيان
    /// </summary>
    [Key]
    public int Id { get; set; }

    /// <summary>
    /// تاريخ الإنشاء
    /// </summary>
    [Required]
    public DateTime CreatedAt { get; set; } = DateTime.Now;

    /// <summary>
    /// تاريخ آخر تحديث
    /// </summary>
    public DateTime? UpdatedAt { get; set; }

    /// <summary>
    /// معرف المستخدم الذي أنشأ السجل
    /// </summary>
    public int? CreatedBy { get; set; }

    /// <summary>
    /// معرف المستخدم الذي حدث السجل آخر مرة
    /// </summary>
    public int? UpdatedBy { get; set; }

    /// <summary>
    /// هل السجل محذوف (حذف ناعم)
    /// </summary>
    public bool IsDeleted { get; set; } = false;

    /// <summary>
    /// تاريخ الحذف
    /// </summary>
    public DateTime? DeletedAt { get; set; }

    /// <summary>
    /// معرف المستخدم الذي حذف السجل
    /// </summary>
    public int? DeletedBy { get; set; }

    /// <summary>
    /// رقم الإصدار للتحكم في التزامن المتفائل
    /// </summary>
    [Timestamp]
    public byte[]? RowVersion { get; set; }

    /// <summary>
    /// ملاحظات إضافية
    /// </summary>
    [StringLength(1000)]
    public string? Notes { get; set; }

    /// <summary>
    /// تحديث معلومات التعديل
    /// </summary>
    /// <param name="userId">معرف المستخدم</param>
    public virtual void UpdateModificationInfo(int? userId = null)
    {
        UpdatedAt = DateTime.Now;
        UpdatedBy = userId;
    }

    /// <summary>
    /// تحديث معلومات الحذف الناعم
    /// </summary>
    /// <param name="userId">معرف المستخدم</param>
    public virtual void SoftDelete(int? userId = null)
    {
        IsDeleted = true;
        DeletedAt = DateTime.Now;
        DeletedBy = userId;
    }

    /// <summary>
    /// استعادة السجل المحذوف
    /// </summary>
    /// <param name="userId">معرف المستخدم</param>
    public virtual void Restore(int? userId = null)
    {
        IsDeleted = false;
        DeletedAt = null;
        DeletedBy = null;
        UpdateModificationInfo(userId);
    }
}
