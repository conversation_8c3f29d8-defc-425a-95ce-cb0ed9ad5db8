using Microsoft.EntityFrameworkCore;
using CarDealershipManagement.Enhanced.Data;
using CarDealershipManagement.Enhanced.Models;
using CarDealershipManagement.Enhanced.Enums;
using CarDealershipManagement.Enhanced.Services.Interfaces;

namespace CarDealershipManagement.Enhanced.Services;

/// <summary>
/// خدمة السيارات
/// </summary>
public class CarService : BaseService<Car>, ICarService
{
    public CarService(CarDealershipDbContext context) : base(context)
    {
    }

    protected override IQueryable<Car> ApplyIncludes(IQueryable<Car> query)
    {
        return query
            .Include(c => c.Supplier)
            .Include(c => c.Customer)
            .Include(c => c.CarImages)
            .Include(c => c.CarDocuments)
            .Include(c => c.Sales);
    }

    public async Task<Car?> GetByChassisNumberAsync(string chassisNumber)
    {
        return await ApplyIncludes(_dbSet)
            .FirstOrDefaultAsync(c => c.ChassisNumber == chassisNumber);
    }

    public async Task<IEnumerable<Car>> GetByStatusAsync(CarStatus status)
    {
        return await ApplyIncludes(_dbSet)
            .Where(c => c.Status == status)
            .ToListAsync();
    }

    public async Task<IEnumerable<Car>> GetAvailableCarsAsync()
    {
        return await GetByStatusAsync(CarStatus.Available);
    }

    public async Task<IEnumerable<Car>> GetSoldCarsAsync()
    {
        return await GetByStatusAsync(CarStatus.Sold);
    }

    public async Task<IEnumerable<Car>> GetBySupplierAsync(int supplierId)
    {
        return await ApplyIncludes(_dbSet)
            .Where(c => c.SupplierId == supplierId)
            .ToListAsync();
    }

    public async Task<IEnumerable<Car>> GetByCustomerAsync(int customerId)
    {
        return await ApplyIncludes(_dbSet)
            .Where(c => c.CustomerId == customerId)
            .ToListAsync();
    }

    public async Task<IEnumerable<Car>> SearchCarsAsync(
        string? brand = null,
        string? model = null,
        int? year = null,
        string? color = null,
        decimal? minPrice = null,
        decimal? maxPrice = null,
        CarStatus? status = null)
    {
        var query = ApplyIncludes(_dbSet).AsQueryable();

        if (!string.IsNullOrEmpty(brand))
            query = query.Where(c => c.Brand.Contains(brand));

        if (!string.IsNullOrEmpty(model))
            query = query.Where(c => c.Model.Contains(model));

        if (year.HasValue)
            query = query.Where(c => c.Year == year.Value);

        if (!string.IsNullOrEmpty(color))
            query = query.Where(c => c.Color.Contains(color));

        if (minPrice.HasValue)
            query = query.Where(c => c.SalePrice >= minPrice.Value);

        if (maxPrice.HasValue)
            query = query.Where(c => c.SalePrice <= maxPrice.Value);

        if (status.HasValue)
            query = query.Where(c => c.Status == status.Value);

        return await query.ToListAsync();
    }

    public async Task<bool> UpdateCarStatusAsync(int carId, CarStatus status, int? userId = null)
    {
        var car = await GetByIdAsync(carId);
        if (car == null) return false;

        car.Status = status;
        car.UpdateModificationInfo(userId);

        await UpdateAsync(car, userId);
        return true;
    }

    public async Task<bool> UpdateCarPriceAsync(int carId, decimal newPrice, int? userId = null)
    {
        var car = await GetByIdAsync(carId);
        if (car == null) return false;

        car.SalePrice = newPrice;
        car.UpdateModificationInfo(userId);

        await UpdateAsync(car, userId);
        return true;
    }

    public async Task<CarImage> AddCarImageAsync(int carId, CarImage image, int? userId = null)
    {
        image.CarId = carId;
        image.CreatedAt = DateTime.Now;
        image.CreatedBy = userId;

        _context.CarImages.Add(image);
        await _context.SaveChangesAsync();

        return image;
    }

    public async Task<CarDocument> AddCarDocumentAsync(int carId, CarDocument document, int? userId = null)
    {
        document.CarId = carId;
        document.CreatedAt = DateTime.Now;
        document.CreatedBy = userId;

        _context.CarDocuments.Add(document);
        await _context.SaveChangesAsync();

        return document;
    }

    public async Task<IEnumerable<CarImage>> GetCarImagesAsync(int carId)
    {
        return await _context.CarImages
            .Where(i => i.CarId == carId)
            .OrderBy(i => i.SortOrder)
            .ThenByDescending(i => i.IsPrimary)
            .ToListAsync();
    }

    public async Task<IEnumerable<CarDocument>> GetCarDocumentsAsync(int carId)
    {
        return await _context.CarDocuments
            .Where(d => d.CarId == carId)
            .OrderBy(d => d.DocumentType)
            .ThenBy(d => d.CreatedAt)
            .ToListAsync();
    }

    public async Task<bool> SetPrimaryImageAsync(int carId, int imageId, int? userId = null)
    {
        // إزالة الصورة الرئيسية الحالية
        var currentPrimary = await _context.CarImages
            .Where(i => i.CarId == carId && i.IsPrimary)
            .ToListAsync();

        foreach (var img in currentPrimary)
        {
            img.IsPrimary = false;
            img.UpdateModificationInfo(userId);
        }

        // تعيين الصورة الجديدة كرئيسية
        var newPrimary = await _context.CarImages
            .FirstOrDefaultAsync(i => i.Id == imageId && i.CarId == carId);

        if (newPrimary == null) return false;

        newPrimary.IsPrimary = true;
        newPrimary.UpdateModificationInfo(userId);

        await _context.SaveChangesAsync();
        return true;
    }

    public async Task<bool> DeleteCarImageAsync(int imageId, int? userId = null)
    {
        var image = await _context.CarImages.FindAsync(imageId);
        if (image == null) return false;

        image.SoftDelete(userId);
        await _context.SaveChangesAsync();
        return true;
    }

    public async Task<bool> DeleteCarDocumentAsync(int documentId, int? userId = null)
    {
        var document = await _context.CarDocuments.FindAsync(documentId);
        if (document == null) return false;

        document.SoftDelete(userId);
        await _context.SaveChangesAsync();
        return true;
    }

    public async Task<MaintenanceRecord> AddMaintenanceRecordAsync(int carId, MaintenanceRecord maintenanceRecord, int? userId = null)
    {
        maintenanceRecord.CarId = carId;
        maintenanceRecord.CreatedAt = DateTime.Now;
        maintenanceRecord.CreatedBy = userId;

        _context.MaintenanceRecords.Add(maintenanceRecord);
        await _context.SaveChangesAsync();

        return maintenanceRecord;
    }

    public async Task<IEnumerable<MaintenanceRecord>> GetMaintenanceRecordsAsync(int carId)
    {
        return await _context.MaintenanceRecords
            .Where(m => m.CarId == carId)
            .OrderByDescending(m => m.MaintenanceDate)
            .ToListAsync();
    }

    public async Task<CarStatistics> GetCarStatisticsAsync()
    {
        var totalCars = await _dbSet.CountAsync();
        var availableCars = await _dbSet.CountAsync(c => c.Status == CarStatus.Available);
        var soldCars = await _dbSet.CountAsync(c => c.Status == CarStatus.Sold);
        var reservedCars = await _dbSet.CountAsync(c => c.Status == CarStatus.Reserved);
        var carsInMaintenance = await _dbSet.CountAsync(c => c.Status == CarStatus.InMaintenance);

        var inventoryValue = await _dbSet
            .Where(c => c.Status != CarStatus.Sold)
            .SumAsync(c => c.SalePrice);

        var averagePrice = totalCars > 0 ? await _dbSet.AverageAsync(c => c.SalePrice) : 0;
        var highestPrice = totalCars > 0 ? await _dbSet.MaxAsync(c => c.SalePrice) : 0;
        var lowestPrice = totalCars > 0 ? await _dbSet.MinAsync(c => c.SalePrice) : 0;

        var thisMonth = DateTime.Now.Date.AddDays(-DateTime.Now.Day + 1);
        var carsAddedThisMonth = await _dbSet.CountAsync(c => c.CreatedAt >= thisMonth);
        var carsSoldThisMonth = await _dbSet.CountAsync(c => c.Status == CarStatus.Sold && c.UpdatedAt >= thisMonth);

        return new CarStatistics
        {
            TotalCars = totalCars,
            AvailableCars = availableCars,
            SoldCars = soldCars,
            ReservedCars = reservedCars,
            CarsInMaintenance = carsInMaintenance,
            TotalInventoryValue = inventoryValue,
            AverageCarPrice = averagePrice,
            HighestCarPrice = highestPrice,
            LowestCarPrice = lowestPrice,
            CarsAddedThisMonth = carsAddedThisMonth,
            CarsSoldThisMonth = carsSoldThisMonth
        };
    }

    public async Task<IEnumerable<Car>> GetCarsNeedingMaintenanceAsync()
    {
        return await ApplyIncludes(_dbSet)
            .Where(c => c.Status == CarStatus.InMaintenance || 
                       (c.NextMaintenanceDate.HasValue && c.NextMaintenanceDate.Value <= DateTime.Now.AddDays(30)))
            .ToListAsync();
    }

    public async Task<IEnumerable<Car>> GetTopSellingCarsAsync(int count = 10)
    {
        return await _context.Sales
            .Where(s => s.Status == SaleStatus.Confirmed)
            .GroupBy(s => s.CarId)
            .OrderByDescending(g => g.Count())
            .Take(count)
            .Select(g => g.First().Car)
            .ToListAsync();
    }

    public async Task<bool> IsChassisNumberAvailableAsync(string chassisNumber, int? excludeCarId = null)
    {
        var query = _dbSet.Where(c => c.ChassisNumber == chassisNumber);
        
        if (excludeCarId.HasValue)
            query = query.Where(c => c.Id != excludeCarId.Value);

        return !await query.AnyAsync();
    }

    protected override async Task<bool> ValidateEntityAsync(Car entity)
    {
        // التحقق من عدم تكرار الرقم التسلسلي
        if (!await IsChassisNumberAvailableAsync(entity.ChassisNumber, entity.Id))
            return false;

        // التحقق من صحة البيانات الأساسية
        if (string.IsNullOrEmpty(entity.Brand) || 
            string.IsNullOrEmpty(entity.Model) || 
            string.IsNullOrEmpty(entity.ChassisNumber))
            return false;

        return true;
    }

    protected override async Task OnAfterUpdateAsync(Car entity, int? userId = null)
    {
        // إذا تم تغيير حالة السيارة إلى مباعة، تحديث حالة العميل
        if (entity.Status == CarStatus.Sold && entity.CustomerId.HasValue)
        {
            var customer = await _context.Customers.FindAsync(entity.CustomerId.Value);
            if (customer != null)
            {
                customer.LastPurchaseDate = DateTime.Now;
                customer.UpdateModificationInfo(userId);
            }
        }

        await base.OnAfterUpdateAsync(entity, userId);
    }
}
