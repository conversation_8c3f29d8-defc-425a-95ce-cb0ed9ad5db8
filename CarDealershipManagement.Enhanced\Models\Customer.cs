using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using CarDealershipManagement.Enhanced.Enums;

namespace CarDealershipManagement.Enhanced.Models;

/// <summary>
/// نموذج العميل
/// </summary>
[Table("Customers")]
public class Customer : BaseEntity
{
    /// <summary>
    /// الاسم الأول
    /// </summary>
    [Required(ErrorMessage = "الاسم الأول مطلوب")]
    [StringLength(100, ErrorMessage = "الاسم الأول يجب أن يكون أقل من 100 حرف")]
    [Column("FirstName")]
    public string FirstName { get; set; } = string.Empty;

    /// <summary>
    /// الاسم الأخير
    /// </summary>
    [Required(ErrorMessage = "الاسم الأخير مطلوب")]
    [StringLength(100, ErrorMessage = "الاسم الأخير يجب أن يكون أقل من 100 حرف")]
    [Column("LastName")]
    public string LastName { get; set; } = string.Empty;

    /// <summary>
    /// رقم الهوية
    /// </summary>
    [Required(ErrorMessage = "رقم الهوية مطلوب")]
    [StringLength(20, ErrorMessage = "رقم الهوية يجب أن يكون أقل من 20 حرف")]
    [Column("IdentityNumber")]
    public string IdentityNumber { get; set; } = string.Empty;

    /// <summary>
    /// رقم الهاتف
    /// </summary>
    [Required(ErrorMessage = "رقم الهاتف مطلوب")]
    [StringLength(20, ErrorMessage = "رقم الهاتف يجب أن يكون أقل من 20 حرف")]
    [Phone(ErrorMessage = "رقم الهاتف غير صحيح")]
    [Column("Phone")]
    public string Phone { get; set; } = string.Empty;

    /// <summary>
    /// رقم هاتف إضافي
    /// </summary>
    [StringLength(20)]
    [Phone(ErrorMessage = "رقم الهاتف الإضافي غير صحيح")]
    [Column("AlternativePhone")]
    public string? AlternativePhone { get; set; }

    /// <summary>
    /// البريد الإلكتروني
    /// </summary>
    [StringLength(200)]
    [EmailAddress(ErrorMessage = "البريد الإلكتروني غير صحيح")]
    [Column("Email")]
    public string? Email { get; set; }

    /// <summary>
    /// العنوان
    /// </summary>
    [Required(ErrorMessage = "العنوان مطلوب")]
    [StringLength(500, ErrorMessage = "العنوان يجب أن يكون أقل من 500 حرف")]
    [Column("Address")]
    public string Address { get; set; } = string.Empty;

    /// <summary>
    /// المدينة
    /// </summary>
    [Required(ErrorMessage = "المدينة مطلوبة")]
    [StringLength(100, ErrorMessage = "المدينة يجب أن تكون أقل من 100 حرف")]
    [Column("City")]
    public string City { get; set; } = string.Empty;

    /// <summary>
    /// المحافظة/المنطقة
    /// </summary>
    [StringLength(100)]
    [Column("State")]
    public string? State { get; set; }

    /// <summary>
    /// الرمز البريدي
    /// </summary>
    [StringLength(10)]
    [Column("PostalCode")]
    public string? PostalCode { get; set; }

    /// <summary>
    /// الدولة
    /// </summary>
    [StringLength(100)]
    [Column("Country")]
    public string? Country { get; set; } = "المملكة العربية السعودية";

    /// <summary>
    /// تاريخ الميلاد
    /// </summary>
    [Column("DateOfBirth")]
    public DateTime? DateOfBirth { get; set; }

    /// <summary>
    /// الجنس
    /// </summary>
    [StringLength(10)]
    [Column("Gender")]
    public string? Gender { get; set; }

    /// <summary>
    /// الجنسية
    /// </summary>
    [StringLength(100)]
    [Column("Nationality")]
    public string? Nationality { get; set; }

    /// <summary>
    /// المهنة
    /// </summary>
    [StringLength(200)]
    [Column("Occupation")]
    public string? Occupation { get; set; }

    /// <summary>
    /// جهة العمل
    /// </summary>
    [StringLength(200)]
    [Column("Employer")]
    public string? Employer { get; set; }

    /// <summary>
    /// الراتب الشهري
    /// </summary>
    [Range(0, double.MaxValue, ErrorMessage = "الراتب يجب أن يكون أكبر من أو يساوي صفر")]
    [Column("MonthlySalary", TypeName = "decimal(18,2)")]
    public decimal? MonthlySalary { get; set; }

    /// <summary>
    /// حالة العميل
    /// </summary>
    [Required]
    [Column("Status")]
    public CustomerStatus Status { get; set; } = CustomerStatus.Active;

    /// <summary>
    /// تقييم العميل (من 1 إلى 5)
    /// </summary>
    [Range(1, 5, ErrorMessage = "التقييم يجب أن يكون بين 1 و 5")]
    [Column("Rating")]
    public int? Rating { get; set; }

    /// <summary>
    /// مصدر العميل
    /// </summary>
    [StringLength(200)]
    [Column("Source")]
    public string? Source { get; set; }

    /// <summary>
    /// معرف مندوب المبيعات المسؤول
    /// </summary>
    [Column("SalesAgentId")]
    public int? SalesAgentId { get; set; }

    /// <summary>
    /// تاريخ أول تعامل
    /// </summary>
    [Column("FirstContactDate")]
    public DateTime? FirstContactDate { get; set; }

    /// <summary>
    /// تاريخ آخر تعامل
    /// </summary>
    [Column("LastContactDate")]
    public DateTime? LastContactDate { get; set; }

    /// <summary>
    /// الحد الائتماني
    /// </summary>
    [Range(0, double.MaxValue, ErrorMessage = "الحد الائتماني يجب أن يكون أكبر من أو يساوي صفر")]
    [Column("CreditLimit", TypeName = "decimal(18,2)")]
    public decimal CreditLimit { get; set; } = 0;

    /// <summary>
    /// الرصيد الحالي
    /// </summary>
    [Column("CurrentBalance", TypeName = "decimal(18,2)")]
    public decimal CurrentBalance { get; set; } = 0;

    /// <summary>
    /// هل العميل موافق على التسويق
    /// </summary>
    [Column("MarketingConsent")]
    public bool MarketingConsent { get; set; } = false;

    /// <summary>
    /// اللغة المفضلة
    /// </summary>
    [StringLength(10)]
    [Column("PreferredLanguage")]
    public string? PreferredLanguage { get; set; } = "ar";

    /// <summary>
    /// طريقة التواصل المفضلة
    /// </summary>
    [StringLength(50)]
    [Column("PreferredContactMethod")]
    public string? PreferredContactMethod { get; set; }

    /// <summary>
    /// ملاحظات خاصة
    /// </summary>
    [StringLength(2000)]
    [Column("SpecialNotes")]
    public string? SpecialNotes { get; set; }

    // Navigation Properties

    /// <summary>
    /// مندوب المبيعات المسؤول
    /// </summary>
    [ForeignKey("SalesAgentId")]
    public virtual User? SalesAgent { get; set; }

    /// <summary>
    /// المبيعات المرتبطة بالعميل
    /// </summary>
    public virtual ICollection<Sale> Sales { get; set; } = new List<Sale>();

    /// <summary>
    /// السيارات المشتراة
    /// </summary>
    public virtual ICollection<Car> Cars { get; set; } = new List<Car>();

    /// <summary>
    /// مستندات العميل
    /// </summary>
    public virtual ICollection<CustomerDocument> CustomerDocuments { get; set; } = new List<CustomerDocument>();

    /// <summary>
    /// الأقساط المرتبطة بالعميل
    /// </summary>
    public virtual ICollection<InstallmentPayment> InstallmentPayments { get; set; } = new List<InstallmentPayment>();

    /// <summary>
    /// سجل التفاعلات مع العميل
    /// </summary>
    public virtual ICollection<CustomerInteraction> CustomerInteractions { get; set; } = new List<CustomerInteraction>();

    // Computed Properties

    /// <summary>
    /// الاسم الكامل
    /// </summary>
    [NotMapped]
    public string FullName => $"{FirstName} {LastName}";

    /// <summary>
    /// العمر
    /// </summary>
    [NotMapped]
    public int? Age => DateOfBirth.HasValue ? DateTime.Now.Year - DateOfBirth.Value.Year : null;

    /// <summary>
    /// إجمالي المشتريات
    /// </summary>
    [NotMapped]
    public decimal TotalPurchases => Sales?.Where(s => s.Status == SaleStatus.Completed).Sum(s => s.TotalAmount) ?? 0;

    /// <summary>
    /// عدد السيارات المشتراة
    /// </summary>
    [NotMapped]
    public int TotalCarsPurchased => Sales?.Count(s => s.Status == SaleStatus.Completed) ?? 0;

    /// <summary>
    /// هل العميل VIP
    /// </summary>
    [NotMapped]
    public bool IsVIP => Status == CustomerStatus.VIP || TotalPurchases > 100000;

    /// <summary>
    /// الرصيد المتاح
    /// </summary>
    [NotMapped]
    public decimal AvailableCredit => CreditLimit - CurrentBalance;

    /// <summary>
    /// عدد أيام آخر تعامل
    /// </summary>
    [NotMapped]
    public int? DaysSinceLastContact => LastContactDate.HasValue ? (DateTime.Now - LastContactDate.Value).Days : null;
}
