using Microsoft.Extensions.DependencyInjection;
using CarDealershipManagement.Enhanced.Services.Interfaces;

namespace CarDealershipManagement.Enhanced.Forms;

public partial class CarsForm : Form
{
    private readonly IServiceProvider _serviceProvider;
    private readonly ICarService _carService;

    public CarsForm(IServiceProvider serviceProvider)
    {
        _serviceProvider = serviceProvider;
        _carService = serviceProvider.GetRequiredService<ICarService>();
        
        InitializeComponent();
        LoadData();
    }

    private async void LoadData()
    {
        try
        {
            // سيتم تنفيذ تحميل البيانات لاحقاً
            lblStatus.Text = "جاري تحميل بيانات السيارات...";
            await Task.Delay(1000); // محاكاة التحميل
            lblStatus.Text = "تم تحميل البيانات بنجاح";
        }
        catch (Exception ex)
        {
            MessageBox.Show($"خطأ في تحميل البيانات: {ex.Message}", "خطأ", 
                MessageBoxButtons.OK, MessageBoxIcon.Error);
        }
    }
}

public partial class CarsForm
{
    private System.ComponentModel.IContainer components = null;
    private Label lblStatus;

    protected override void Dispose(bool disposing)
    {
        if (disposing && (components != null))
        {
            components.Dispose();
        }
        base.Dispose(disposing);
    }

    private void InitializeComponent()
    {
        lblStatus = new Label();
        SuspendLayout();
        
        // lblStatus
        lblStatus.AutoSize = true;
        lblStatus.Font = new Font("Segoe UI", 12F);
        lblStatus.Location = new Point(50, 50);
        lblStatus.Name = "lblStatus";
        lblStatus.Size = new Size(200, 28);
        lblStatus.TabIndex = 0;
        lblStatus.Text = "وحدة إدارة السيارات";
        
        // CarsForm
        AutoScaleDimensions = new SizeF(8F, 20F);
        AutoScaleMode = AutoScaleMode.Font;
        ClientSize = new Size(800, 600);
        Controls.Add(lblStatus);
        Name = "CarsForm";
        RightToLeft = RightToLeft.Yes;
        Text = "إدارة السيارات";
        ResumeLayout(false);
        PerformLayout();
    }
}
