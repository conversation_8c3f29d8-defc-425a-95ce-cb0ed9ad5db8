using FluentValidation;
using CarDealershipManagement.Enhanced.Models;
using CarDealershipManagement.Enhanced.Services.Interfaces;
using CarDealershipManagement.Enhanced.Enums;

namespace CarDealershipManagement.Enhanced.Validators;

/// <summary>
/// فئة التحقق من صحة بيانات المستخدم
/// </summary>
public class UserValidator : AbstractValidator<User>
{
    private readonly IUserService _userService;

    public UserValidator(IUserService userService)
    {
        _userService = userService;

        RuleFor(u => u.FirstName)
            .NotEmpty().WithMessage("الاسم الأول مطلوب")
            .Length(2, 50).WithMessage("الاسم الأول يجب أن يكون بين 2 و 50 حرف")
            .Matches(@"^[\u0600-\u06FFa-zA-Z\s]+$").WithMessage("الاسم الأول يجب أن يحتوي على أحرف فقط");

        RuleFor(u => u.LastName)
            .NotEmpty().WithMessage("الاسم الأخير مطلوب")
            .Length(2, 50).WithMessage("الاسم الأخير يجب أن يكون بين 2 و 50 حرف")
            .Matches(@"^[\u0600-\u06FFa-zA-Z\s]+$").WithMessage("الاسم الأخير يجب أن يحتوي على أحرف فقط");

        RuleFor(u => u.Username)
            .NotEmpty().WithMessage("اسم المستخدم مطلوب")
            .Length(3, 30).WithMessage("اسم المستخدم يجب أن يكون بين 3 و 30 حرف")
            .Matches(@"^[a-zA-Z0-9_]+$").WithMessage("اسم المستخدم يجب أن يحتوي على أحرف وأرقام و _ فقط")
            .MustAsync(BeUniqueUsername).WithMessage("اسم المستخدم موجود مسبقاً");

        When(u => !string.IsNullOrEmpty(u.Email), () =>
        {
            RuleFor(u => u.Email)
                .EmailAddress().WithMessage("البريد الإلكتروني غير صحيح")
                .MustAsync(BeUniqueEmail).WithMessage("البريد الإلكتروني موجود مسبقاً");
        });

        RuleFor(u => u.Phone)
            .NotEmpty().WithMessage("رقم الهاتف مطلوب")
            .Matches(@"^(\+?[0-9]{10,15})$").WithMessage("رقم الهاتف غير صحيح");

        RuleFor(u => u.Role)
            .IsInEnum().WithMessage("دور المستخدم غير صحيح");

        RuleFor(u => u.HireDate)
            .LessThanOrEqualTo(DateTime.Now).WithMessage("تاريخ التوظيف لا يمكن أن يكون في المستقبل");

        RuleFor(u => u.Salary)
            .GreaterThanOrEqualTo(0).WithMessage("الراتب لا يمكن أن يكون سالب")
            .LessThan(1000000).WithMessage("الراتب يجب أن يكون أقل من مليون");

        When(u => u.ManagerId.HasValue, () =>
        {
            RuleFor(u => u.ManagerId)
                .GreaterThan(0).WithMessage("معرف المدير غير صحيح")
                .NotEqual(u => u.Id).WithMessage("المستخدم لا يمكن أن يكون مديراً لنفسه");
        });

        // التحقق من كلمة المرور عند الإنشاء
        When(u => u.Id == 0, () =>
        {
            RuleFor(u => u.PasswordHash)
                .NotEmpty().WithMessage("كلمة المرور مطلوبة");
        });
    }

    private async Task<bool> BeUniqueUsername(User user, string username, CancellationToken cancellationToken)
    {
        return await _userService.IsUsernameAvailableAsync(username, user.Id);
    }

    private async Task<bool> BeUniqueEmail(User user, string email, CancellationToken cancellationToken)
    {
        return await _userService.IsEmailAvailableAsync(email, user.Id);
    }
}

/// <summary>
/// فئة التحقق من صحة بيانات المبيعة
/// </summary>
public class SaleValidator : AbstractValidator<Sale>
{
    public SaleValidator()
    {
        RuleFor(s => s.CarId)
            .GreaterThan(0).WithMessage("السيارة مطلوبة");

        RuleFor(s => s.CustomerId)
            .GreaterThan(0).WithMessage("العميل مطلوب");

        RuleFor(s => s.SalesAgentId)
            .GreaterThan(0).WithMessage("مندوب المبيعات مطلوب");

        RuleFor(s => s.SalePrice)
            .GreaterThan(0).WithMessage("سعر البيع يجب أن يكون أكبر من صفر")
            .LessThan(10000000).WithMessage("سعر البيع يجب أن يكون أقل من 10 مليون");

        RuleFor(s => s.DownPayment)
            .GreaterThanOrEqualTo(0).WithMessage("الدفعة المقدمة لا يمكن أن تكون سالبة")
            .LessThanOrEqualTo(s => s.SalePrice).WithMessage("الدفعة المقدمة لا يمكن أن تكون أكبر من سعر البيع");

        RuleFor(s => s.Discount)
            .GreaterThanOrEqualTo(0).WithMessage("الخصم لا يمكن أن يكون سالب")
            .LessThan(s => s.SalePrice).WithMessage("الخصم لا يمكن أن يكون أكبر من أو يساوي سعر البيع");

        RuleFor(s => s.TaxAmount)
            .GreaterThanOrEqualTo(0).WithMessage("مبلغ الضريبة لا يمكن أن يكون سالب");

        RuleFor(s => s.SaleDate)
            .LessThanOrEqualTo(DateTime.Now).WithMessage("تاريخ البيع لا يمكن أن يكون في المستقبل");

        RuleFor(s => s.Status)
            .IsInEnum().WithMessage("حالة البيع غير صحيحة");

        When(s => s.NumberOfInstallments > 0, () =>
        {
            RuleFor(s => s.NumberOfInstallments)
                .InclusiveBetween(1, 120).WithMessage("عدد الأقساط يجب أن يكون بين 1 و 120");

            RuleFor(s => s.FirstInstallmentDate)
                .NotNull().WithMessage("تاريخ أول قسط مطلوب عند وجود أقساط")
                .GreaterThan(DateTime.Today).WithMessage("تاريخ أول قسط يجب أن يكون في المستقبل");
        });

        // التحقق من أن المبلغ الإجمالي صحيح
        RuleFor(s => s)
            .Must(s => s.TotalAmount == s.SalePrice - s.Discount + s.TaxAmount)
            .WithMessage("المبلغ الإجمالي غير صحيح");
    }
}

/// <summary>
/// فئة التحقق من صحة بيانات القسط
/// </summary>
public class InstallmentPaymentValidator : AbstractValidator<InstallmentPayment>
{
    public InstallmentPaymentValidator()
    {
        RuleFor(i => i.SaleId)
            .GreaterThan(0).WithMessage("معرف البيع مطلوب");

        RuleFor(i => i.InstallmentNumber)
            .GreaterThan(0).WithMessage("رقم القسط يجب أن يكون أكبر من صفر")
            .LessThanOrEqualTo(120).WithMessage("رقم القسط يجب أن يكون أقل من أو يساوي 120");

        RuleFor(i => i.Amount)
            .GreaterThan(0).WithMessage("مبلغ القسط يجب أن يكون أكبر من صفر")
            .LessThan(10000000).WithMessage("مبلغ القسط يجب أن يكون أقل من 10 مليون");

        RuleFor(i => i.PaidAmount)
            .GreaterThanOrEqualTo(0).WithMessage("المبلغ المدفوع لا يمكن أن يكون سالب")
            .LessThanOrEqualTo(i => i.Amount + i.LateFee).WithMessage("المبلغ المدفوع لا يمكن أن يكون أكبر من مبلغ القسط مع الغرامة");

        RuleFor(i => i.LateFee)
            .GreaterThanOrEqualTo(0).WithMessage("الغرامة لا يمكن أن تكون سالبة");

        RuleFor(i => i.DueDate)
            .GreaterThan(DateTime.Today.AddYears(-1)).WithMessage("تاريخ الاستحقاق لا يمكن أن يكون قبل سنة من اليوم");

        RuleFor(i => i.Status)
            .IsInEnum().WithMessage("حالة القسط غير صحيحة");

        When(i => i.PaymentDate.HasValue, () =>
        {
            RuleFor(i => i.PaymentDate)
                .LessThanOrEqualTo(DateTime.Now).WithMessage("تاريخ الدفع لا يمكن أن يكون في المستقبل");

            RuleFor(i => i.PaymentType)
                .IsInEnum().WithMessage("نوع الدفع غير صحيح");
        });

        When(i => i.Status == InstallmentStatus.Paid, () =>
        {
            RuleFor(i => i.PaymentDate)
                .NotNull().WithMessage("تاريخ الدفع مطلوب عند دفع القسط");

            RuleFor(i => i.PaidAmount)
                .GreaterThan(0).WithMessage("المبلغ المدفوع مطلوب عند دفع القسط");
        });
    }
}

/// <summary>
/// فئة التحقق من صحة بيانات صلاحية المستخدم
/// </summary>
public class UserPermissionValidator : AbstractValidator<UserPermission>
{
    public UserPermissionValidator()
    {
        RuleFor(p => p.UserId)
            .GreaterThan(0).WithMessage("معرف المستخدم مطلوب");

        RuleFor(p => p.PermissionName)
            .NotEmpty().WithMessage("اسم الصلاحية مطلوب")
            .Length(3, 100).WithMessage("اسم الصلاحية يجب أن يكون بين 3 و 100 حرف");

        When(p => !string.IsNullOrEmpty(p.Description), () =>
        {
            RuleFor(p => p.Description)
                .MaximumLength(500).WithMessage("وصف الصلاحية يجب أن يكون أقل من 500 حرف");
        });

        When(p => p.ExpiryDate.HasValue, () =>
        {
            RuleFor(p => p.ExpiryDate)
                .GreaterThan(DateTime.Now).WithMessage("تاريخ انتهاء الصلاحية يجب أن يكون في المستقبل");
        });
    }
}

/// <summary>
/// فئة التحقق من صحة بيانات المستند
/// </summary>
public class DocumentValidator : AbstractValidator<BaseDocument>
{
    public DocumentValidator()
    {
        RuleFor(d => d.FileName)
            .NotEmpty().WithMessage("اسم الملف مطلوب")
            .Length(1, 255).WithMessage("اسم الملف يجب أن يكون بين 1 و 255 حرف");

        RuleFor(d => d.FilePath)
            .NotEmpty().WithMessage("مسار الملف مطلوب")
            .Length(1, 500).WithMessage("مسار الملف يجب أن يكون بين 1 و 500 حرف");

        RuleFor(d => d.FileSize)
            .GreaterThan(0).WithMessage("حجم الملف يجب أن يكون أكبر من صفر")
            .LessThan(100 * 1024 * 1024).WithMessage("حجم الملف يجب أن يكون أقل من 100 ميجابايت");

        RuleFor(d => d.DocumentType)
            .IsInEnum().WithMessage("نوع المستند غير صحيح");

        When(d => !string.IsNullOrEmpty(d.Description), () =>
        {
            RuleFor(d => d.Description)
                .MaximumLength(1000).WithMessage("وصف المستند يجب أن يكون أقل من 1000 حرف");
        });

        When(d => d.ExpiryDate.HasValue, () =>
        {
            RuleFor(d => d.ExpiryDate)
                .GreaterThan(DateTime.Today).WithMessage("تاريخ انتهاء المستند يجب أن يكون في المستقبل");
        });
    }
}
