using CarDealershipManagement.Enhanced.Models;
using CarDealershipManagement.Enhanced.Enums;

namespace CarDealershipManagement.Enhanced.Services.Interfaces;

/// <summary>
/// واجهة خدمة المستخدمين
/// </summary>
public interface IUserService : IBaseService<User>
{
    /// <summary>
    /// تسجيل دخول المستخدم
    /// </summary>
    /// <param name="username">اسم المستخدم</param>
    /// <param name="password">كلمة المرور</param>
    /// <returns>المستخدم إذا نجح تسجيل الدخول، null إذا فشل</returns>
    Task<User?> LoginAsync(string username, string password);

    /// <summary>
    /// تسجيل خروج المستخدم
    /// </summary>
    /// <param name="userId">معرف المستخدم</param>
    /// <returns>true إذا تم تسجيل الخروج بنجاح</returns>
    Task<bool> LogoutAsync(int userId);

    /// <summary>
    /// البحث في المستخدمين باسم المستخدم
    /// </summary>
    /// <param name="username">اسم المستخدم</param>
    /// <returns>المستخدم أو null</returns>
    Task<User?> GetByUsernameAsync(string username);

    /// <summary>
    /// البحث في المستخدمين بالبريد الإلكتروني
    /// </summary>
    /// <param name="email">البريد الإلكتروني</param>
    /// <returns>المستخدم أو null</returns>
    Task<User?> GetByEmailAsync(string email);

    /// <summary>
    /// الحصول على المستخدمين حسب الدور
    /// </summary>
    /// <param name="role">الدور</param>
    /// <returns>قائمة المستخدمين</returns>
    Task<IEnumerable<User>> GetByRoleAsync(UserRole role);

    /// <summary>
    /// الحصول على المستخدمين النشطين
    /// </summary>
    /// <returns>قائمة المستخدمين النشطين</returns>
    Task<IEnumerable<User>> GetActiveUsersAsync();

    /// <summary>
    /// الحصول على المستخدمين المحظورين
    /// </summary>
    /// <returns>قائمة المستخدمين المحظورين</returns>
    Task<IEnumerable<User>> GetLockedUsersAsync();

    /// <summary>
    /// الحصول على المستخدمين حسب المدير
    /// </summary>
    /// <param name="managerId">معرف المدير</param>
    /// <returns>قائمة المستخدمين</returns>
    Task<IEnumerable<User>> GetByManagerAsync(int managerId);

    /// <summary>
    /// تغيير كلمة مرور المستخدم
    /// </summary>
    /// <param name="userId">معرف المستخدم</param>
    /// <param name="currentPassword">كلمة المرور الحالية</param>
    /// <param name="newPassword">كلمة المرور الجديدة</param>
    /// <returns>true إذا تم التغيير بنجاح</returns>
    Task<bool> ChangePasswordAsync(int userId, string currentPassword, string newPassword);

    /// <summary>
    /// إعادة تعيين كلمة مرور المستخدم (للمدير)
    /// </summary>
    /// <param name="userId">معرف المستخدم</param>
    /// <param name="newPassword">كلمة المرور الجديدة</param>
    /// <param name="adminUserId">معرف المدير</param>
    /// <returns>true إذا تم إعادة التعيين بنجاح</returns>
    Task<bool> ResetPasswordAsync(int userId, string newPassword, int adminUserId);

    /// <summary>
    /// حظر المستخدم
    /// </summary>
    /// <param name="userId">معرف المستخدم</param>
    /// <param name="reason">سبب الحظر</param>
    /// <param name="adminUserId">معرف المدير</param>
    /// <returns>true إذا تم الحظر بنجاح</returns>
    Task<bool> LockUserAsync(int userId, string reason, int adminUserId);

    /// <summary>
    /// إلغاء حظر المستخدم
    /// </summary>
    /// <param name="userId">معرف المستخدم</param>
    /// <param name="adminUserId">معرف المدير</param>
    /// <returns>true إذا تم إلغاء الحظر بنجاح</returns>
    Task<bool> UnlockUserAsync(int userId, int adminUserId);

    /// <summary>
    /// إضافة صلاحية للمستخدم
    /// </summary>
    /// <param name="userId">معرف المستخدم</param>
    /// <param name="permission">الصلاحية</param>
    /// <param name="adminUserId">معرف المدير</param>
    /// <returns>الصلاحية المضافة</returns>
    Task<UserPermission> AddUserPermissionAsync(int userId, UserPermission permission, int adminUserId);

    /// <summary>
    /// إزالة صلاحية من المستخدم
    /// </summary>
    /// <param name="userId">معرف المستخدم</param>
    /// <param name="permissionName">اسم الصلاحية</param>
    /// <param name="adminUserId">معرف المدير</param>
    /// <returns>true إذا تم الإزالة بنجاح</returns>
    Task<bool> RemoveUserPermissionAsync(int userId, string permissionName, int adminUserId);

    /// <summary>
    /// الحصول على صلاحيات المستخدم
    /// </summary>
    /// <param name="userId">معرف المستخدم</param>
    /// <returns>قائمة الصلاحيات</returns>
    Task<IEnumerable<UserPermission>> GetUserPermissionsAsync(int userId);

    /// <summary>
    /// التحقق من صلاحية المستخدم
    /// </summary>
    /// <param name="userId">معرف المستخدم</param>
    /// <param name="permissionName">اسم الصلاحية</param>
    /// <returns>true إذا كان المستخدم يملك الصلاحية</returns>
    Task<bool> HasPermissionAsync(int userId, string permissionName);

    /// <summary>
    /// الحصول على سجل تسجيل دخول المستخدم
    /// </summary>
    /// <param name="userId">معرف المستخدم</param>
    /// <param name="startDate">تاريخ البداية</param>
    /// <param name="endDate">تاريخ النهاية</param>
    /// <returns>قائمة سجلات تسجيل الدخول</returns>
    Task<IEnumerable<UserLoginLog>> GetUserLoginLogsAsync(int userId, DateTime? startDate = null, DateTime? endDate = null);

    /// <summary>
    /// الحصول على مبيعات المستخدم (إذا كان مندوب مبيعات)
    /// </summary>
    /// <param name="userId">معرف المستخدم</param>
    /// <param name="startDate">تاريخ البداية</param>
    /// <param name="endDate">تاريخ النهاية</param>
    /// <returns>قائمة المبيعات</returns>
    Task<IEnumerable<Sale>> GetUserSalesAsync(int userId, DateTime? startDate = null, DateTime? endDate = null);

    /// <summary>
    /// الحصول على عملاء المستخدم (إذا كان مندوب مبيعات)
    /// </summary>
    /// <param name="userId">معرف المستخدم</param>
    /// <returns>قائمة العملاء</returns>
    Task<IEnumerable<Customer>> GetUserCustomersAsync(int userId);

    /// <summary>
    /// حساب عمولات المستخدم
    /// </summary>
    /// <param name="userId">معرف المستخدم</param>
    /// <param name="startDate">تاريخ البداية</param>
    /// <param name="endDate">تاريخ النهاية</param>
    /// <returns>إجمالي العمولات</returns>
    Task<decimal> CalculateUserCommissionsAsync(int userId, DateTime? startDate = null, DateTime? endDate = null);

    /// <summary>
    /// الحصول على إحصائيات المستخدمين
    /// </summary>
    /// <returns>إحصائيات المستخدمين</returns>
    Task<UserStatistics> GetUserStatisticsAsync();

    /// <summary>
    /// البحث في المستخدمين بمعايير متقدمة
    /// </summary>
    /// <param name="searchTerm">مصطلح البحث</param>
    /// <param name="role">الدور</param>
    /// <param name="isActive">نشط</param>
    /// <param name="managerId">معرف المدير</param>
    /// <returns>قائمة المستخدمين المطابقة</returns>
    Task<IEnumerable<User>> SearchUsersAsync(
        string? searchTerm = null,
        UserRole? role = null,
        bool? isActive = null,
        int? managerId = null);

    /// <summary>
    /// التحقق من توفر اسم المستخدم
    /// </summary>
    /// <param name="username">اسم المستخدم</param>
    /// <param name="excludeUserId">معرف المستخدم المستثنى (للتحديث)</param>
    /// <returns>true إذا كان الاسم متاح</returns>
    Task<bool> IsUsernameAvailableAsync(string username, int? excludeUserId = null);

    /// <summary>
    /// التحقق من توفر البريد الإلكتروني
    /// </summary>
    /// <param name="email">البريد الإلكتروني</param>
    /// <param name="excludeUserId">معرف المستخدم المستثنى (للتحديث)</param>
    /// <returns>true إذا كان البريد متاح</returns>
    Task<bool> IsEmailAvailableAsync(string email, int? excludeUserId = null);

    /// <summary>
    /// التحقق من قوة كلمة المرور
    /// </summary>
    /// <param name="password">كلمة المرور</param>
    /// <returns>نتيجة التحقق من قوة كلمة المرور</returns>
    Task<PasswordStrengthResult> CheckPasswordStrengthAsync(string password);

    /// <summary>
    /// إنشاء كلمة مرور عشوائية قوية
    /// </summary>
    /// <param name="length">طول كلمة المرور</param>
    /// <returns>كلمة المرور المولدة</returns>
    Task<string> GenerateStrongPasswordAsync(int length = 12);
}

/// <summary>
/// إحصائيات المستخدمين
/// </summary>
public class UserStatistics
{
    /// <summary>
    /// العدد الإجمالي للمستخدمين
    /// </summary>
    public int TotalUsers { get; set; }

    /// <summary>
    /// عدد المستخدمين النشطين
    /// </summary>
    public int ActiveUsers { get; set; }

    /// <summary>
    /// عدد المستخدمين المحظورين
    /// </summary>
    public int LockedUsers { get; set; }

    /// <summary>
    /// عدد المديرين
    /// </summary>
    public int Administrators { get; set; }

    /// <summary>
    /// عدد مندوبي المبيعات
    /// </summary>
    public int SalesAgents { get; set; }

    /// <summary>
    /// عدد الموظفين
    /// </summary>
    public int Employees { get; set; }

    /// <summary>
    /// عدد المستخدمين الجدد هذا الشهر
    /// </summary>
    public int NewUsersThisMonth { get; set; }

    /// <summary>
    /// عدد تسجيلات الدخول اليوم
    /// </summary>
    public int LoginsToday { get; set; }

    /// <summary>
    /// عدد المستخدمين المتصلين حالياً
    /// </summary>
    public int OnlineUsers { get; set; }

    /// <summary>
    /// متوسط عدد تسجيلات الدخول يومياً
    /// </summary>
    public double AverageDailyLogins { get; set; }
}

/// <summary>
/// نتيجة التحقق من قوة كلمة المرور
/// </summary>
public class PasswordStrengthResult
{
    /// <summary>
    /// مستوى القوة (1-5)
    /// </summary>
    public int StrengthLevel { get; set; }

    /// <summary>
    /// النقاط المحققة
    /// </summary>
    public int Score { get; set; }

    /// <summary>
    /// الحد الأقصى للنقاط
    /// </summary>
    public int MaxScore { get; set; }

    /// <summary>
    /// هل كلمة المرور قوية بما فيه الكفاية
    /// </summary>
    public bool IsStrong { get; set; }

    /// <summary>
    /// التوصيات لتحسين كلمة المرور
    /// </summary>
    public List<string> Recommendations { get; set; } = new();

    /// <summary>
    /// وصف مستوى القوة
    /// </summary>
    public string StrengthDescription { get; set; } = string.Empty;
}
