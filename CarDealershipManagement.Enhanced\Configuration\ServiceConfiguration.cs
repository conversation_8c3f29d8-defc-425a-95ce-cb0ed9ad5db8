using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Configuration;
using Microsoft.EntityFrameworkCore;
using FluentValidation;
using AutoMapper;
using Serilog;
using CarDealershipManagement.Enhanced.Data;
using CarDealershipManagement.Enhanced.Services;
using CarDealershipManagement.Enhanced.Services.Interfaces;
using CarDealershipManagement.Enhanced.Validators;
using CarDealershipManagement.Enhanced.Models;

namespace CarDealershipManagement.Enhanced.Configuration;

/// <summary>
/// تكوين خدمات حقن التبعية
/// </summary>
public static class ServiceConfiguration
{
    /// <summary>
    /// تكوين جميع الخدمات المطلوبة للتطبيق
    /// </summary>
    public static IServiceCollection ConfigureServices(this IServiceCollection services, IConfiguration configuration)
    {
        // تكوين قاعدة البيانات
        services.AddDbContext<CarDealershipDbContext>(options =>
        {
            var connectionString = configuration.GetConnectionString("DefaultConnection");
            options.UseSqlite(connectionString);
            
            // تمكين التسجيل الحساس للبيانات في بيئة التطوير
            if (configuration.GetValue<bool>("Logging:EnableSensitiveDataLogging"))
            {
                options.EnableSensitiveDataLogging();
            }
            
            // تمكين التسجيل التفصيلي في بيئة التطوير
            if (configuration.GetValue<bool>("Logging:EnableDetailedErrors"))
            {
                options.EnableDetailedErrors();
            }
        });

        // تكوين خدمات الأعمال
        services.AddScoped<ICarService, CarService>();
        services.AddScoped<ICustomerService, CustomerService>();
        services.AddScoped<ISaleService, SaleService>();
        services.AddScoped<ISupplierService, SupplierService>();
        services.AddScoped<IUserService, UserService>();
        services.AddScoped<IInstallmentService, InstallmentService>();

        // تكوين فئات التحقق من صحة البيانات
        services.AddScoped<IValidator<Car>, CarValidator>();
        services.AddScoped<IValidator<Customer>, CustomerValidator>();
        services.AddScoped<IValidator<Sale>, SaleValidator>();
        services.AddScoped<IValidator<Supplier>, SupplierValidator>();
        services.AddScoped<IValidator<User>, UserValidator>();
        services.AddScoped<IValidator<InstallmentPayment>, InstallmentPaymentValidator>();
        services.AddScoped<IValidator<UserPermission>, UserPermissionValidator>();
        services.AddScoped<IValidator<BaseDocument>, DocumentValidator>();

        // تكوين AutoMapper
        services.AddAutoMapper(typeof(ServiceConfiguration).Assembly);

        // تكوين Serilog
        services.AddLogging(loggingBuilder =>
        {
            var logger = new LoggerConfiguration()
                .ReadFrom.Configuration(configuration)
                .CreateLogger();
            
            loggingBuilder.AddSerilog(logger);
        });

        return services;
    }

    /// <summary>
    /// تكوين خدمات إضافية للتطبيق
    /// </summary>
    public static IServiceCollection ConfigureAdditionalServices(this IServiceCollection services, IConfiguration configuration)
    {
        // تكوين خدمات الأمان
        services.AddScoped<ISecurityService, SecurityService>();
        services.AddScoped<IPermissionService, PermissionService>();
        services.AddScoped<IAuditService, AuditService>();

        // تكوين خدمات الملفات
        services.AddScoped<IFileService, FileService>();
        services.AddScoped<IDocumentService, DocumentService>();
        services.AddScoped<IImageService, ImageService>();

        // تكوين خدمات التقارير
        services.AddScoped<IReportService, ReportService>();
        services.AddScoped<IStatisticsService, StatisticsService>();

        // تكوين خدمات النسخ الاحتياطية
        services.AddScoped<IBackupService, BackupService>();

        // تكوين خدمات الإشعارات
        services.AddScoped<INotificationService, NotificationService>();
        services.AddScoped<IReminderService, ReminderService>();

        return services;
    }

    /// <summary>
    /// تكوين خيارات التطبيق
    /// </summary>
    public static IServiceCollection ConfigureOptions(this IServiceCollection services, IConfiguration configuration)
    {
        // تكوين خيارات قاعدة البيانات
        services.Configure<DatabaseOptions>(configuration.GetSection("Database"));

        // تكوين خيارات الأمان
        services.Configure<SecurityOptions>(configuration.GetSection("Security"));

        // تكوين خيارات الملفات
        services.Configure<FileOptions>(configuration.GetSection("Files"));

        // تكوين خيارات النسخ الاحتياطية
        services.Configure<BackupOptions>(configuration.GetSection("Backup"));

        // تكوين خيارات التقارير
        services.Configure<ReportOptions>(configuration.GetSection("Reports"));

        // تكوين خيارات الإشعارات
        services.Configure<NotificationOptions>(configuration.GetSection("Notifications"));

        return services;
    }
}

/// <summary>
/// خيارات قاعدة البيانات
/// </summary>
public class DatabaseOptions
{
    public string ConnectionString { get; set; } = string.Empty;
    public bool EnableMigrations { get; set; } = true;
    public bool EnableSeedData { get; set; } = true;
    public int CommandTimeout { get; set; } = 30;
    public bool EnableRetryOnFailure { get; set; } = true;
    public int MaxRetryCount { get; set; } = 3;
}

/// <summary>
/// خيارات الأمان
/// </summary>
public class SecurityOptions
{
    public int PasswordMinLength { get; set; } = 8;
    public int MaxLoginAttempts { get; set; } = 5;
    public int LockoutDurationMinutes { get; set; } = 30;
    public int SessionTimeoutMinutes { get; set; } = 60;
    public bool RequirePasswordChange { get; set; } = false;
    public int PasswordExpiryDays { get; set; } = 90;
    public bool EnableTwoFactorAuth { get; set; } = false;
    public string EncryptionKey { get; set; } = string.Empty;
}

/// <summary>
/// خيارات الملفات
/// </summary>
public class FileOptions
{
    public string BasePath { get; set; } = "Files";
    public string CarFilesPath { get; set; } = "CarFiles";
    public string ImagesPath { get; set; } = "Images";
    public string DocumentsPath { get; set; } = "Documents";
    public string BackupsPath { get; set; } = "Backups";
    public long MaxFileSize { get; set; } = 100 * 1024 * 1024; // 100 MB
    public string[] AllowedImageExtensions { get; set; } = { ".jpg", ".jpeg", ".png", ".gif", ".bmp" };
    public string[] AllowedDocumentExtensions { get; set; } = { ".pdf", ".doc", ".docx", ".xls", ".xlsx", ".txt" };
    public bool EnableCompression { get; set; } = true;
    public bool EnableThumbnails { get; set; } = true;
}

/// <summary>
/// خيارات النسخ الاحتياطية
/// </summary>
public class BackupOptions
{
    public bool EnableAutoBackup { get; set; } = true;
    public int BackupIntervalHours { get; set; } = 24;
    public int MaxBackupFiles { get; set; } = 30;
    public bool EnableCompression { get; set; } = true;
    public bool EnableEncryption { get; set; } = false;
    public string BackupPath { get; set; } = "Backups";
    public string BackupFileNameFormat { get; set; } = "CarDealership_Backup_{0:yyyyMMdd_HHmmss}.db";
}

/// <summary>
/// خيارات التقارير
/// </summary>
public class ReportOptions
{
    public string ReportsPath { get; set; } = "Reports";
    public string TemplatePath { get; set; } = "Templates";
    public bool EnablePdfExport { get; set; } = true;
    public bool EnableExcelExport { get; set; } = true;
    public bool EnableWordExport { get; set; } = true;
    public string CompanyName { get; set; } = "شركة إدارة معارض السيارات";
    public string CompanyLogo { get; set; } = "Resources/logo.png";
    public string ReportFooter { get; set; } = "تم إنشاء هذا التقرير بواسطة نظام إدارة معارض السيارات";
}

/// <summary>
/// خيارات الإشعارات
/// </summary>
public class NotificationOptions
{
    public bool EnableEmailNotifications { get; set; } = false;
    public bool EnableSmsNotifications { get; set; } = false;
    public bool EnableInstallmentReminders { get; set; } = true;
    public int ReminderDaysBefore { get; set; } = 3;
    public bool EnableMaintenanceReminders { get; set; } = true;
    public bool EnableDocumentExpiryReminders { get; set; } = true;
    public int DocumentExpiryReminderDays { get; set; } = 30;
    
    // إعدادات البريد الإلكتروني
    public string SmtpServer { get; set; } = string.Empty;
    public int SmtpPort { get; set; } = 587;
    public string SmtpUsername { get; set; } = string.Empty;
    public string SmtpPassword { get; set; } = string.Empty;
    public bool SmtpEnableSsl { get; set; } = true;
    public string FromEmail { get; set; } = string.Empty;
    public string FromName { get; set; } = "نظام إدارة معارض السيارات";
    
    // إعدادات الرسائل النصية
    public string SmsApiUrl { get; set; } = string.Empty;
    public string SmsApiKey { get; set; } = string.Empty;
    public string SmsUsername { get; set; } = string.Empty;
    public string SmsPassword { get; set; } = string.Empty;
}
