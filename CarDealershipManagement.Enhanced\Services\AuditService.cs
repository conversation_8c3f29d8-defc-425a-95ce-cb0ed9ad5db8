using Microsoft.EntityFrameworkCore;
using CarDealershipManagement.Enhanced.Data;
using CarDealershipManagement.Enhanced.Models;
using CarDealershipManagement.Enhanced.Enums;
using System.Text.Json;

namespace CarDealershipManagement.Enhanced.Services;

/// <summary>
/// واجهة خدمة التدقيق
/// </summary>
public interface IAuditService
{
    Task LogAsync(string tableName, int recordId, AuditAction action, object? oldValues = null, object? newValues = null, int? userId = null);
    Task<IEnumerable<AuditLog>> GetAuditLogsAsync(string? tableName = null, int? recordId = null, int? userId = null, DateTime? startDate = null, DateTime? endDate = null);
    Task<IEnumerable<AuditLog>> GetUserActivityAsync(int userId, DateTime? startDate = null, DateTime? endDate = null);
    Task<IEnumerable<AuditLog>> GetTableChangesAsync(string tableName, int? recordId = null, DateTime? startDate = null, DateTime? endDate = null);
    Task<AuditStatistics> GetAuditStatisticsAsync(DateTime? startDate = null, DateTime? endDate = null);
    Task CleanupOldLogsAsync(int daysToKeep = 365);
}

/// <summary>
/// خدمة التدقيق
/// </summary>
public class AuditService : IAuditService
{
    private readonly CarDealershipDbContext _context;

    public AuditService(CarDealershipDbContext context)
    {
        _context = context;
    }

    public async Task LogAsync(string tableName, int recordId, AuditAction action, object? oldValues = null, object? newValues = null, int? userId = null)
    {
        var auditLog = new AuditLog
        {
            TableName = tableName,
            RecordId = recordId,
            Action = action,
            OldValues = oldValues != null ? JsonSerializer.Serialize(oldValues, new JsonSerializerOptions { WriteIndented = false }) : null,
            NewValues = newValues != null ? JsonSerializer.Serialize(newValues, new JsonSerializerOptions { WriteIndented = false }) : null,
            UserId = userId,
            Timestamp = DateTime.Now
        };

        _context.AuditLogs.Add(auditLog);
        await _context.SaveChangesAsync();
    }

    public async Task<IEnumerable<AuditLog>> GetAuditLogsAsync(string? tableName = null, int? recordId = null, int? userId = null, DateTime? startDate = null, DateTime? endDate = null)
    {
        var query = _context.AuditLogs.AsQueryable();

        if (!string.IsNullOrEmpty(tableName))
            query = query.Where(a => a.TableName == tableName);

        if (recordId.HasValue)
            query = query.Where(a => a.RecordId == recordId.Value);

        if (userId.HasValue)
            query = query.Where(a => a.UserId == userId.Value);

        if (startDate.HasValue)
            query = query.Where(a => a.Timestamp >= startDate.Value);

        if (endDate.HasValue)
            query = query.Where(a => a.Timestamp <= endDate.Value);

        return await query
            .OrderByDescending(a => a.Timestamp)
            .ToListAsync();
    }

    public async Task<IEnumerable<AuditLog>> GetUserActivityAsync(int userId, DateTime? startDate = null, DateTime? endDate = null)
    {
        var query = _context.AuditLogs.Where(a => a.UserId == userId);

        if (startDate.HasValue)
            query = query.Where(a => a.Timestamp >= startDate.Value);

        if (endDate.HasValue)
            query = query.Where(a => a.Timestamp <= endDate.Value);

        return await query
            .OrderByDescending(a => a.Timestamp)
            .ToListAsync();
    }

    public async Task<IEnumerable<AuditLog>> GetTableChangesAsync(string tableName, int? recordId = null, DateTime? startDate = null, DateTime? endDate = null)
    {
        var query = _context.AuditLogs.Where(a => a.TableName == tableName);

        if (recordId.HasValue)
            query = query.Where(a => a.RecordId == recordId.Value);

        if (startDate.HasValue)
            query = query.Where(a => a.Timestamp >= startDate.Value);

        if (endDate.HasValue)
            query = query.Where(a => a.Timestamp <= endDate.Value);

        return await query
            .OrderByDescending(a => a.Timestamp)
            .ToListAsync();
    }

    public async Task<AuditStatistics> GetAuditStatisticsAsync(DateTime? startDate = null, DateTime? endDate = null)
    {
        var query = _context.AuditLogs.AsQueryable();

        if (startDate.HasValue)
            query = query.Where(a => a.Timestamp >= startDate.Value);

        if (endDate.HasValue)
            query = query.Where(a => a.Timestamp <= endDate.Value);

        var totalLogs = await query.CountAsync();
        var insertLogs = await query.CountAsync(a => a.Action == AuditAction.Insert);
        var updateLogs = await query.CountAsync(a => a.Action == AuditAction.Update);
        var deleteLogs = await query.CountAsync(a => a.Action == AuditAction.Delete);
        var securityLogs = await query.CountAsync(a => a.Action == AuditAction.Security);

        var topUsers = await query
            .Where(a => a.UserId.HasValue)
            .GroupBy(a => a.UserId)
            .Select(g => new { UserId = g.Key, Count = g.Count() })
            .OrderByDescending(x => x.Count)
            .Take(10)
            .ToListAsync();

        var topTables = await query
            .GroupBy(a => a.TableName)
            .Select(g => new { TableName = g.Key, Count = g.Count() })
            .OrderByDescending(x => x.Count)
            .Take(10)
            .ToListAsync();

        var dailyActivity = await query
            .GroupBy(a => a.Timestamp.Date)
            .Select(g => new { Date = g.Key, Count = g.Count() })
            .OrderByDescending(x => x.Date)
            .Take(30)
            .ToListAsync();

        return new AuditStatistics
        {
            TotalLogs = totalLogs,
            InsertLogs = insertLogs,
            UpdateLogs = updateLogs,
            DeleteLogs = deleteLogs,
            SecurityLogs = securityLogs,
            TopUsers = topUsers.ToDictionary(x => x.UserId ?? 0, x => x.Count),
            TopTables = topTables.ToDictionary(x => x.TableName, x => x.Count),
            DailyActivity = dailyActivity.ToDictionary(x => x.Date, x => x.Count)
        };
    }

    public async Task CleanupOldLogsAsync(int daysToKeep = 365)
    {
        var cutoffDate = DateTime.Now.AddDays(-daysToKeep);
        
        var oldLogs = await _context.AuditLogs
            .Where(a => a.Timestamp < cutoffDate)
            .ToListAsync();

        if (oldLogs.Any())
        {
            _context.AuditLogs.RemoveRange(oldLogs);
            await _context.SaveChangesAsync();
        }
    }
}

/// <summary>
/// إحصائيات التدقيق
/// </summary>
public class AuditStatistics
{
    public int TotalLogs { get; set; }
    public int InsertLogs { get; set; }
    public int UpdateLogs { get; set; }
    public int DeleteLogs { get; set; }
    public int SecurityLogs { get; set; }
    public Dictionary<int, int> TopUsers { get; set; } = new();
    public Dictionary<string, int> TopTables { get; set; } = new();
    public Dictionary<DateTime, int> DailyActivity { get; set; } = new();
}

/// <summary>
/// واجهة خدمة الملفات
/// </summary>
public interface IFileService
{
    Task<string> SaveFileAsync(byte[] fileData, string fileName, string subDirectory = "");
    Task<byte[]> ReadFileAsync(string filePath);
    Task<bool> DeleteFileAsync(string filePath);
    Task<bool> FileExistsAsync(string filePath);
    Task<long> GetFileSizeAsync(string filePath);
    Task<string> GetFileExtensionAsync(string fileName);
    Task<bool> IsValidFileExtensionAsync(string fileName, string[] allowedExtensions);
    Task<string> GenerateUniqueFileNameAsync(string originalFileName, string directory);
    Task<byte[]> CompressFileAsync(byte[] fileData);
    Task<byte[]> DecompressFileAsync(byte[] compressedData);
}

/// <summary>
/// خدمة الملفات
/// </summary>
public class FileService : IFileService
{
    private readonly FileOptions _fileOptions;

    public FileService(Microsoft.Extensions.Options.IOptions<FileOptions> fileOptions)
    {
        _fileOptions = fileOptions.Value;
    }

    public async Task<string> SaveFileAsync(byte[] fileData, string fileName, string subDirectory = "")
    {
        var directory = Path.Combine(_fileOptions.BasePath, subDirectory);
        Directory.CreateDirectory(directory);

        var uniqueFileName = await GenerateUniqueFileNameAsync(fileName, directory);
        var filePath = Path.Combine(directory, uniqueFileName);

        if (_fileOptions.EnableCompression)
        {
            fileData = await CompressFileAsync(fileData);
        }

        await File.WriteAllBytesAsync(filePath, fileData);
        return filePath;
    }

    public async Task<byte[]> ReadFileAsync(string filePath)
    {
        if (!await FileExistsAsync(filePath))
            throw new FileNotFoundException($"الملف غير موجود: {filePath}");

        var fileData = await File.ReadAllBytesAsync(filePath);

        if (_fileOptions.EnableCompression)
        {
            fileData = await DecompressFileAsync(fileData);
        }

        return fileData;
    }

    public async Task<bool> DeleteFileAsync(string filePath)
    {
        return await Task.Run(() =>
        {
            try
            {
                if (File.Exists(filePath))
                {
                    File.Delete(filePath);
                    return true;
                }
                return false;
            }
            catch
            {
                return false;
            }
        });
    }

    public async Task<bool> FileExistsAsync(string filePath)
    {
        return await Task.Run(() => File.Exists(filePath));
    }

    public async Task<long> GetFileSizeAsync(string filePath)
    {
        return await Task.Run(() =>
        {
            var fileInfo = new FileInfo(filePath);
            return fileInfo.Exists ? fileInfo.Length : 0;
        });
    }

    public async Task<string> GetFileExtensionAsync(string fileName)
    {
        return await Task.Run(() => Path.GetExtension(fileName).ToLowerInvariant());
    }

    public async Task<bool> IsValidFileExtensionAsync(string fileName, string[] allowedExtensions)
    {
        var extension = await GetFileExtensionAsync(fileName);
        return allowedExtensions.Contains(extension);
    }

    public async Task<string> GenerateUniqueFileNameAsync(string originalFileName, string directory)
    {
        return await Task.Run(() =>
        {
            var fileName = Path.GetFileNameWithoutExtension(originalFileName);
            var extension = Path.GetExtension(originalFileName);
            var counter = 1;
            var uniqueFileName = originalFileName;

            while (File.Exists(Path.Combine(directory, uniqueFileName)))
            {
                uniqueFileName = $"{fileName}_{counter}{extension}";
                counter++;
            }

            return uniqueFileName;
        });
    }

    public async Task<byte[]> CompressFileAsync(byte[] fileData)
    {
        return await Task.Run(() =>
        {
            using var output = new MemoryStream();
            using var gzip = new System.IO.Compression.GZipStream(output, System.IO.Compression.CompressionMode.Compress);
            gzip.Write(fileData, 0, fileData.Length);
            gzip.Close();
            return output.ToArray();
        });
    }

    public async Task<byte[]> DecompressFileAsync(byte[] compressedData)
    {
        return await Task.Run(() =>
        {
            using var input = new MemoryStream(compressedData);
            using var gzip = new System.IO.Compression.GZipStream(input, System.IO.Compression.CompressionMode.Decompress);
            using var output = new MemoryStream();
            gzip.CopyTo(output);
            return output.ToArray();
        });
    }
}
