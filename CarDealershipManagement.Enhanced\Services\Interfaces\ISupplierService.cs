using CarDealershipManagement.Enhanced.Models;
using CarDealershipManagement.Enhanced.Enums;

namespace CarDealershipManagement.Enhanced.Services.Interfaces;

/// <summary>
/// واجهة خدمة الموردين
/// </summary>
public interface ISupplierService : IBaseService<Supplier>
{
    /// <summary>
    /// البحث في الموردين برقم التسجيل التجاري
    /// </summary>
    /// <param name="registrationNumber">رقم التسجيل التجاري</param>
    /// <returns>المورد أو null</returns>
    Task<Supplier?> GetByRegistrationNumberAsync(string registrationNumber);

    /// <summary>
    /// البحث في الموردين برقم الهاتف
    /// </summary>
    /// <param name="phone">رقم الهاتف</param>
    /// <returns>المورد أو null</returns>
    Task<Supplier?> GetByPhoneAsync(string phone);

    /// <summary>
    /// البحث في الموردين بالبريد الإلكتروني
    /// </summary>
    /// <param name="email">البريد الإلكتروني</param>
    /// <returns>المورد أو null</returns>
    Task<Supplier?> GetByEmailAsync(string email);

    /// <summary>
    /// الحصول على الموردين حسب الحالة
    /// </summary>
    /// <param name="status">الحالة</param>
    /// <returns>قائمة الموردين</returns>
    Task<IEnumerable<Supplier>> GetByStatusAsync(SupplierStatus status);

    /// <summary>
    /// الحصول على الموردين النشطين
    /// </summary>
    /// <returns>قائمة الموردين النشطين</returns>
    Task<IEnumerable<Supplier>> GetActiveSuppliers();

    /// <summary>
    /// البحث في الموردين بمعايير متقدمة
    /// </summary>
    /// <param name="searchTerm">مصطلح البحث</param>
    /// <param name="status">الحالة</param>
    /// <param name="city">المدينة</param>
    /// <param name="country">البلد</param>
    /// <returns>قائمة الموردين المطابقة</returns>
    Task<IEnumerable<Supplier>> SearchSuppliersAsync(
        string? searchTerm = null,
        SupplierStatus? status = null,
        string? city = null,
        string? country = null);

    /// <summary>
    /// تحديث حالة المورد
    /// </summary>
    /// <param name="supplierId">معرف المورد</param>
    /// <param name="status">الحالة الجديدة</param>
    /// <param name="userId">معرف المستخدم</param>
    /// <returns>true إذا تم التحديث بنجاح</returns>
    Task<bool> UpdateSupplierStatusAsync(int supplierId, SupplierStatus status, int? userId = null);

    /// <summary>
    /// تحديث تقييم المورد
    /// </summary>
    /// <param name="supplierId">معرف المورد</param>
    /// <param name="rating">التقييم الجديد</param>
    /// <param name="userId">معرف المستخدم</param>
    /// <returns>true إذا تم التحديث بنجاح</returns>
    Task<bool> UpdateSupplierRatingAsync(int supplierId, int rating, int? userId = null);

    /// <summary>
    /// إضافة مستند للمورد
    /// </summary>
    /// <param name="supplierId">معرف المورد</param>
    /// <param name="document">بيانات المستند</param>
    /// <param name="userId">معرف المستخدم</param>
    /// <returns>المستند المضاف</returns>
    Task<SupplierDocument> AddSupplierDocumentAsync(int supplierId, SupplierDocument document, int? userId = null);

    /// <summary>
    /// الحصول على مستندات المورد
    /// </summary>
    /// <param name="supplierId">معرف المورد</param>
    /// <returns>قائمة المستندات</returns>
    Task<IEnumerable<SupplierDocument>> GetSupplierDocumentsAsync(int supplierId);

    /// <summary>
    /// حذف مستند المورد
    /// </summary>
    /// <param name="documentId">معرف المستند</param>
    /// <param name="userId">معرف المستخدم</param>
    /// <returns>true إذا تم الحذف بنجاح</returns>
    Task<bool> DeleteSupplierDocumentAsync(int documentId, int? userId = null);

    /// <summary>
    /// إضافة دفعة للمورد
    /// </summary>
    /// <param name="supplierId">معرف المورد</param>
    /// <param name="payment">بيانات الدفعة</param>
    /// <param name="userId">معرف المستخدم</param>
    /// <returns>الدفعة المضافة</returns>
    Task<SupplierPayment> AddSupplierPaymentAsync(int supplierId, SupplierPayment payment, int? userId = null);

    /// <summary>
    /// الحصول على دفعات المورد
    /// </summary>
    /// <param name="supplierId">معرف المورد</param>
    /// <returns>قائمة الدفعات</returns>
    Task<IEnumerable<SupplierPayment>> GetSupplierPaymentsAsync(int supplierId);

    /// <summary>
    /// إضافة عقد مع المورد
    /// </summary>
    /// <param name="supplierId">معرف المورد</param>
    /// <param name="contract">بيانات العقد</param>
    /// <param name="userId">معرف المستخدم</param>
    /// <returns>العقد المضاف</returns>
    Task<SupplierContract> AddSupplierContractAsync(int supplierId, SupplierContract contract, int? userId = null);

    /// <summary>
    /// الحصول على عقود المورد
    /// </summary>
    /// <param name="supplierId">معرف المورد</param>
    /// <returns>قائمة العقود</returns>
    Task<IEnumerable<SupplierContract>> GetSupplierContractsAsync(int supplierId);

    /// <summary>
    /// الحصول على السيارات المشتراة من المورد
    /// </summary>
    /// <param name="supplierId">معرف المورد</param>
    /// <returns>قائمة السيارات</returns>
    Task<IEnumerable<Car>> GetSupplierCarsAsync(int supplierId);

    /// <summary>
    /// حساب إجمالي المشتريات من المورد
    /// </summary>
    /// <param name="supplierId">معرف المورد</param>
    /// <returns>إجمالي المشتريات</returns>
    Task<decimal> CalculateSupplierTotalPurchasesAsync(int supplierId);

    /// <summary>
    /// حساب الرصيد المستحق للمورد
    /// </summary>
    /// <param name="supplierId">معرف المورد</param>
    /// <returns>الرصيد المستحق</returns>
    Task<decimal> CalculateSupplierOutstandingBalanceAsync(int supplierId);

    /// <summary>
    /// الحصول على إحصائيات الموردين
    /// </summary>
    /// <returns>إحصائيات الموردين</returns>
    Task<SupplierStatistics> GetSupplierStatisticsAsync();

    /// <summary>
    /// الحصول على أفضل الموردين (حسب المشتريات)
    /// </summary>
    /// <param name="count">عدد الموردين</param>
    /// <returns>قائمة أفضل الموردين</returns>
    Task<IEnumerable<Supplier>> GetTopSuppliersAsync(int count = 10);

    /// <summary>
    /// الحصول على الموردين الذين لديهم مستحقات
    /// </summary>
    /// <returns>قائمة الموردين</returns>
    Task<IEnumerable<Supplier>> GetSuppliersWithOutstandingBalanceAsync();

    /// <summary>
    /// التحقق من توفر رقم التسجيل التجاري
    /// </summary>
    /// <param name="registrationNumber">رقم التسجيل التجاري</param>
    /// <param name="excludeSupplierId">معرف المورد المستثنى (للتحديث)</param>
    /// <returns>true إذا كان الرقم متاح</returns>
    Task<bool> IsRegistrationNumberAvailableAsync(string registrationNumber, int? excludeSupplierId = null);

    /// <summary>
    /// التحقق من توفر رقم الهاتف
    /// </summary>
    /// <param name="phone">رقم الهاتف</param>
    /// <param name="excludeSupplierId">معرف المورد المستثنى (للتحديث)</param>
    /// <returns>true إذا كان الرقم متاح</returns>
    Task<bool> IsPhoneAvailableAsync(string phone, int? excludeSupplierId = null);

    /// <summary>
    /// التحقق من توفر البريد الإلكتروني
    /// </summary>
    /// <param name="email">البريد الإلكتروني</param>
    /// <param name="excludeSupplierId">معرف المورد المستثنى (للتحديث)</param>
    /// <returns>true إذا كان البريد متاح</returns>
    Task<bool> IsEmailAvailableAsync(string email, int? excludeSupplierId = null);
}

/// <summary>
/// إحصائيات الموردين
/// </summary>
public class SupplierStatistics
{
    /// <summary>
    /// العدد الإجمالي للموردين
    /// </summary>
    public int TotalSuppliers { get; set; }

    /// <summary>
    /// عدد الموردين النشطين
    /// </summary>
    public int ActiveSuppliers { get; set; }

    /// <summary>
    /// عدد الموردين الجدد هذا الشهر
    /// </summary>
    public int NewSuppliersThisMonth { get; set; }

    /// <summary>
    /// عدد الموردين الذين لديهم مستحقات
    /// </summary>
    public int SuppliersWithOutstandingBalance { get; set; }

    /// <summary>
    /// إجمالي قيمة المشتريات من الموردين
    /// </summary>
    public decimal TotalPurchasesFromSuppliers { get; set; }

    /// <summary>
    /// متوسط قيمة المشتريات من المورد
    /// </summary>
    public decimal AveragePurchasePerSupplier { get; set; }

    /// <summary>
    /// أعلى قيمة مشتريات من مورد
    /// </summary>
    public decimal HighestSupplierPurchase { get; set; }

    /// <summary>
    /// إجمالي المستحقات للموردين
    /// </summary>
    public decimal TotalOutstandingToSuppliers { get; set; }

    /// <summary>
    /// متوسط التقييم
    /// </summary>
    public double AverageRating { get; set; }

    /// <summary>
    /// عدد السيارات المشتراة هذا الشهر
    /// </summary>
    public int CarsPurchasedThisMonth { get; set; }
}
