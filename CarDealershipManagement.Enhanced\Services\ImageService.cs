using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Options;
using CarDealershipManagement.Enhanced.Data;
using CarDealershipManagement.Enhanced.Models;
using CarDealershipManagement.Enhanced.Configuration;
using System.Drawing;
using System.Drawing.Imaging;

namespace CarDealershipManagement.Enhanced.Services;

/// <summary>
/// واجهة خدمة الصور
/// </summary>
public interface IImageService
{
    Task<CarImage> AddCarImageAsync(int carId, byte[] imageData, string fileName, string? description = null, bool isPrimary = false, int? userId = null);
    Task<bool> DeleteCarImageAsync(int imageId, int? userId = null);
    Task<byte[]> GetImageDataAsync(int imageId);
    Task<byte[]> GetThumbnailDataAsync(int imageId);
    Task<IEnumerable<CarImage>> GetCarImagesAsync(int carId);
    Task<CarImage?> GetPrimaryCarImageAsync(int carId);
    Task<bool> SetPrimaryImageAsync(int carId, int imageId, int? userId = null);
    Task<bool> UpdateImageAsync(int imageId, string? description = null, int? userId = null);
    Task<byte[]> CreateThumbnailAsync(byte[] imageData, int width = 150, int height = 150);
    Task<bool> IsValidImageAsync(byte[] imageData);
    Task<string> GetImageFormatAsync(byte[] imageData);
}

/// <summary>
/// خدمة الصور
/// </summary>
public class ImageService : IImageService
{
    private readonly CarDealershipDbContext _context;
    private readonly IFileService _fileService;
    private readonly Configuration.FileOptions _fileOptions;

    public ImageService(CarDealershipDbContext context, IFileService fileService, IOptions<Configuration.FileOptions> fileOptions)
    {
        _context = context;
        _fileService = fileService;
        _fileOptions = fileOptions.Value;
    }

    public async Task<CarImage> AddCarImageAsync(int carId, byte[] imageData, string fileName, string? description = null, bool isPrimary = false, int? userId = null)
    {
        // التحقق من صحة امتداد الملف
        if (!await _fileService.IsValidFileExtensionAsync(fileName, _fileOptions.AllowedImageExtensions))
            throw new ArgumentException("امتداد الصورة غير مسموح");

        // التحقق من حجم الملف
        if (imageData.Length > _fileOptions.MaxFileSize)
            throw new ArgumentException("حجم الصورة كبير جداً");

        // التحقق من صحة الصورة
        if (!await IsValidImageAsync(imageData))
            throw new ArgumentException("الملف ليس صورة صحيحة");

        // حفظ الصورة الأصلية
        var imagePath = await _fileService.SaveFileAsync(imageData, fileName, Path.Combine(_fileOptions.ImagesPath, carId.ToString()));

        // إنشاء صورة مصغرة
        byte[]? thumbnailData = null;
        string? thumbnailPath = null;
        
        if (_fileOptions.EnableThumbnails)
        {
            thumbnailData = await CreateThumbnailAsync(imageData);
            var thumbnailFileName = $"thumb_{fileName}";
            thumbnailPath = await _fileService.SaveFileAsync(thumbnailData, thumbnailFileName, Path.Combine(_fileOptions.ImagesPath, carId.ToString(), "thumbnails"));
        }

        // إذا كانت هذه الصورة الأساسية، إلغاء تعيين الصور الأساسية الأخرى
        if (isPrimary)
        {
            var existingPrimaryImages = await _context.CarImages
                .Where(i => i.CarId == carId && i.IsPrimary && !i.IsDeleted)
                .ToListAsync();

            foreach (var img in existingPrimaryImages)
            {
                img.IsPrimary = false;
                img.UpdateModificationInfo(userId);
            }
        }

        var carImage = new CarImage
        {
            CarId = carId,
            FileName = fileName,
            FilePath = imagePath,
            ThumbnailPath = thumbnailPath,
            FileSize = imageData.Length,
            Description = description,
            IsPrimary = isPrimary,
            CreatedAt = DateTime.Now,
            CreatedBy = userId
        };

        _context.CarImages.Add(carImage);
        await _context.SaveChangesAsync();

        return carImage;
    }

    public async Task<bool> DeleteCarImageAsync(int imageId, int? userId = null)
    {
        var carImage = await _context.CarImages.FindAsync(imageId);
        if (carImage == null || carImage.IsDeleted)
            return false;

        // حذف الملفات
        await _fileService.DeleteFileAsync(carImage.FilePath);
        if (!string.IsNullOrEmpty(carImage.ThumbnailPath))
            await _fileService.DeleteFileAsync(carImage.ThumbnailPath);

        // حذف ناعم من قاعدة البيانات
        carImage.SoftDelete(userId);
        await _context.SaveChangesAsync();

        return true;
    }

    public async Task<byte[]> GetImageDataAsync(int imageId)
    {
        var carImage = await _context.CarImages.FindAsync(imageId);
        if (carImage == null || carImage.IsDeleted)
            throw new FileNotFoundException("الصورة غير موجودة");

        return await _fileService.ReadFileAsync(carImage.FilePath);
    }

    public async Task<byte[]> GetThumbnailDataAsync(int imageId)
    {
        var carImage = await _context.CarImages.FindAsync(imageId);
        if (carImage == null || carImage.IsDeleted)
            throw new FileNotFoundException("الصورة غير موجودة");

        if (string.IsNullOrEmpty(carImage.ThumbnailPath))
        {
            // إنشاء صورة مصغرة إذا لم تكن موجودة
            var originalData = await GetImageDataAsync(imageId);
            return await CreateThumbnailAsync(originalData);
        }

        return await _fileService.ReadFileAsync(carImage.ThumbnailPath);
    }

    public async Task<IEnumerable<CarImage>> GetCarImagesAsync(int carId)
    {
        return await _context.CarImages
            .Where(i => i.CarId == carId && !i.IsDeleted)
            .OrderByDescending(i => i.IsPrimary)
            .ThenByDescending(i => i.CreatedAt)
            .ToListAsync();
    }

    public async Task<CarImage?> GetPrimaryCarImageAsync(int carId)
    {
        return await _context.CarImages
            .FirstOrDefaultAsync(i => i.CarId == carId && i.IsPrimary && !i.IsDeleted);
    }

    public async Task<bool> SetPrimaryImageAsync(int carId, int imageId, int? userId = null)
    {
        var targetImage = await _context.CarImages
            .FirstOrDefaultAsync(i => i.Id == imageId && i.CarId == carId && !i.IsDeleted);

        if (targetImage == null)
            return false;

        // إلغاء تعيين الصور الأساسية الأخرى
        var existingPrimaryImages = await _context.CarImages
            .Where(i => i.CarId == carId && i.IsPrimary && !i.IsDeleted)
            .ToListAsync();

        foreach (var img in existingPrimaryImages)
        {
            img.IsPrimary = false;
            img.UpdateModificationInfo(userId);
        }

        // تعيين الصورة الجديدة كأساسية
        targetImage.IsPrimary = true;
        targetImage.UpdateModificationInfo(userId);

        await _context.SaveChangesAsync();
        return true;
    }

    public async Task<bool> UpdateImageAsync(int imageId, string? description = null, int? userId = null)
    {
        var carImage = await _context.CarImages.FindAsync(imageId);
        if (carImage == null || carImage.IsDeleted)
            return false;

        if (description != null)
            carImage.Description = description;

        carImage.UpdateModificationInfo(userId);
        await _context.SaveChangesAsync();

        return true;
    }

    public async Task<byte[]> CreateThumbnailAsync(byte[] imageData, int width = 150, int height = 150)
    {
        return await Task.Run(() =>
        {
            using var originalStream = new MemoryStream(imageData);
            using var originalImage = Image.FromStream(originalStream);

            // حساب الأبعاد مع الحفاظ على النسبة
            var ratioX = (double)width / originalImage.Width;
            var ratioY = (double)height / originalImage.Height;
            var ratio = Math.Min(ratioX, ratioY);

            var newWidth = (int)(originalImage.Width * ratio);
            var newHeight = (int)(originalImage.Height * ratio);

            using var thumbnail = new Bitmap(newWidth, newHeight);
            using var graphics = Graphics.FromImage(thumbnail);

            graphics.CompositingQuality = System.Drawing.Drawing2D.CompositingQuality.HighQuality;
            graphics.InterpolationMode = System.Drawing.Drawing2D.InterpolationMode.HighQualityBicubic;
            graphics.SmoothingMode = System.Drawing.Drawing2D.SmoothingMode.HighQuality;

            graphics.DrawImage(originalImage, 0, 0, newWidth, newHeight);

            using var thumbnailStream = new MemoryStream();
            thumbnail.Save(thumbnailStream, ImageFormat.Jpeg);
            return thumbnailStream.ToArray();
        });
    }

    public async Task<bool> IsValidImageAsync(byte[] imageData)
    {
        return await Task.Run(() =>
        {
            try
            {
                using var stream = new MemoryStream(imageData);
                using var image = Image.FromStream(stream);
                return true;
            }
            catch
            {
                return false;
            }
        });
    }

    public async Task<string> GetImageFormatAsync(byte[] imageData)
    {
        return await Task.Run(() =>
        {
            try
            {
                using var stream = new MemoryStream(imageData);
                using var image = Image.FromStream(stream);
                
                if (image.RawFormat.Equals(ImageFormat.Jpeg))
                    return "JPEG";
                else if (image.RawFormat.Equals(ImageFormat.Png))
                    return "PNG";
                else if (image.RawFormat.Equals(ImageFormat.Gif))
                    return "GIF";
                else if (image.RawFormat.Equals(ImageFormat.Bmp))
                    return "BMP";
                else
                    return "Unknown";
            }
            catch
            {
                return "Invalid";
            }
        });
    }
}

/// <summary>
/// واجهة خدمة التقارير
/// </summary>
public interface IReportService
{
    Task<byte[]> GenerateSalesReportAsync(DateTime startDate, DateTime endDate, int? salesAgentId = null);
    Task<byte[]> GenerateInventoryReportAsync();
    Task<byte[]> GenerateCustomerReportAsync(int? customerId = null);
    Task<byte[]> GenerateSupplierReportAsync(int? supplierId = null);
    Task<byte[]> GenerateInstallmentReportAsync(DateTime startDate, DateTime endDate, int? customerId = null);
    Task<byte[]> GenerateFinancialReportAsync(DateTime startDate, DateTime endDate);
    Task<byte[]> GenerateUserActivityReportAsync(DateTime startDate, DateTime endDate, int? userId = null);
}

/// <summary>
/// خدمة التقارير
/// </summary>
public class ReportService : IReportService
{
    private readonly CarDealershipDbContext _context;
    private readonly ReportOptions _reportOptions;

    public ReportService(CarDealershipDbContext context, IOptions<ReportOptions> reportOptions)
    {
        _context = context;
        _reportOptions = reportOptions.Value;
    }

    public async Task<byte[]> GenerateSalesReportAsync(DateTime startDate, DateTime endDate, int? salesAgentId = null)
    {
        // سيتم تنفيذ هذه الطريقة لاحقاً مع مكتبة إنشاء التقارير
        await Task.CompletedTask;
        throw new NotImplementedException("سيتم تنفيذ إنشاء التقارير في المرحلة التالية");
    }

    public async Task<byte[]> GenerateInventoryReportAsync()
    {
        await Task.CompletedTask;
        throw new NotImplementedException("سيتم تنفيذ إنشاء التقارير في المرحلة التالية");
    }

    public async Task<byte[]> GenerateCustomerReportAsync(int? customerId = null)
    {
        await Task.CompletedTask;
        throw new NotImplementedException("سيتم تنفيذ إنشاء التقارير في المرحلة التالية");
    }

    public async Task<byte[]> GenerateSupplierReportAsync(int? supplierId = null)
    {
        await Task.CompletedTask;
        throw new NotImplementedException("سيتم تنفيذ إنشاء التقارير في المرحلة التالية");
    }

    public async Task<byte[]> GenerateInstallmentReportAsync(DateTime startDate, DateTime endDate, int? customerId = null)
    {
        await Task.CompletedTask;
        throw new NotImplementedException("سيتم تنفيذ إنشاء التقارير في المرحلة التالية");
    }

    public async Task<byte[]> GenerateFinancialReportAsync(DateTime startDate, DateTime endDate)
    {
        await Task.CompletedTask;
        throw new NotImplementedException("سيتم تنفيذ إنشاء التقارير في المرحلة التالية");
    }

    public async Task<byte[]> GenerateUserActivityReportAsync(DateTime startDate, DateTime endDate, int? userId = null)
    {
        await Task.CompletedTask;
        throw new NotImplementedException("سيتم تنفيذ إنشاء التقارير في المرحلة التالية");
    }
}
