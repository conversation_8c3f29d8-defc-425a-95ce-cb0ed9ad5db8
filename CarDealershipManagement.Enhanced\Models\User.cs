using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using CarDealershipManagement.Enhanced.Enums;

namespace CarDealershipManagement.Enhanced.Models;

/// <summary>
/// نموذج المستخدم
/// </summary>
[Table("Users")]
public class User : BaseEntity
{
    /// <summary>
    /// اسم المستخدم
    /// </summary>
    [Required(ErrorMessage = "اسم المستخدم مطلوب")]
    [StringLength(50, ErrorMessage = "اسم المستخدم يجب أن يكون أقل من 50 حرف")]
    [Column("Username")]
    public string Username { get; set; } = string.Empty;

    /// <summary>
    /// كلمة المرور المشفرة
    /// </summary>
    [Required(ErrorMessage = "كلمة المرور مطلوبة")]
    [StringLength(255)]
    [Column("PasswordHash")]
    public string PasswordHash { get; set; } = string.Empty;

    /// <summary>
    /// الاسم الأول
    /// </summary>
    [Required(ErrorMessage = "الاسم الأول مطلوب")]
    [StringLength(100, ErrorMessage = "الاسم الأول يجب أن يكون أقل من 100 حرف")]
    [Column("FirstName")]
    public string FirstName { get; set; } = string.Empty;

    /// <summary>
    /// الاسم الأخير
    /// </summary>
    [Required(ErrorMessage = "الاسم الأخير مطلوب")]
    [StringLength(100, ErrorMessage = "الاسم الأخير يجب أن يكون أقل من 100 حرف")]
    [Column("LastName")]
    public string LastName { get; set; } = string.Empty;

    /// <summary>
    /// البريد الإلكتروني
    /// </summary>
    [StringLength(200)]
    [EmailAddress(ErrorMessage = "البريد الإلكتروني غير صحيح")]
    [Column("Email")]
    public string? Email { get; set; }

    /// <summary>
    /// رقم الهاتف
    /// </summary>
    [StringLength(20)]
    [Phone(ErrorMessage = "رقم الهاتف غير صحيح")]
    [Column("Phone")]
    public string? Phone { get; set; }

    /// <summary>
    /// دور المستخدم
    /// </summary>
    [Required]
    [Column("Role")]
    public UserRole Role { get; set; } = UserRole.User;

    /// <summary>
    /// هل المستخدم نشط
    /// </summary>
    [Column("IsActive")]
    public bool IsActive { get; set; } = true;

    /// <summary>
    /// تاريخ آخر تسجيل دخول
    /// </summary>
    [Column("LastLoginDate")]
    public DateTime? LastLoginDate { get; set; }

    /// <summary>
    /// عدد محاولات تسجيل الدخول الفاشلة
    /// </summary>
    [Column("FailedLoginAttempts")]
    public int FailedLoginAttempts { get; set; } = 0;

    /// <summary>
    /// تاريخ قفل الحساب
    /// </summary>
    [Column("LockoutEnd")]
    public DateTime? LockoutEnd { get; set; }

    /// <summary>
    /// هل يجب تغيير كلمة المرور في التسجيل التالي
    /// </summary>
    [Column("MustChangePassword")]
    public bool MustChangePassword { get; set; } = false;

    /// <summary>
    /// تاريخ آخر تغيير لكلمة المرور
    /// </summary>
    [Column("PasswordChangedDate")]
    public DateTime? PasswordChangedDate { get; set; }

    /// <summary>
    /// رمز إعادة تعيين كلمة المرور
    /// </summary>
    [StringLength(100)]
    [Column("PasswordResetToken")]
    public string? PasswordResetToken { get; set; }

    /// <summary>
    /// تاريخ انتهاء رمز إعادة التعيين
    /// </summary>
    [Column("PasswordResetExpiry")]
    public DateTime? PasswordResetExpiry { get; set; }

    /// <summary>
    /// المنصب
    /// </summary>
    [StringLength(100)]
    [Column("JobTitle")]
    public string? JobTitle { get; set; }

    /// <summary>
    /// القسم
    /// </summary>
    [StringLength(100)]
    [Column("Department")]
    public string? Department { get; set; }

    /// <summary>
    /// معرف المدير المباشر
    /// </summary>
    [Column("ManagerId")]
    public int? ManagerId { get; set; }

    /// <summary>
    /// الراتب
    /// </summary>
    [Range(0, double.MaxValue, ErrorMessage = "الراتب يجب أن يكون أكبر من أو يساوي صفر")]
    [Column("Salary", TypeName = "decimal(18,2)")]
    public decimal? Salary { get; set; }

    /// <summary>
    /// نسبة العمولة
    /// </summary>
    [Range(0, 100, ErrorMessage = "نسبة العمولة يجب أن تكون بين 0 و 100")]
    [Column("CommissionRate", TypeName = "decimal(5,2)")]
    public decimal CommissionRate { get; set; } = 0;

    /// <summary>
    /// تاريخ التوظيف
    /// </summary>
    [Column("HireDate")]
    public DateTime? HireDate { get; set; }

    /// <summary>
    /// تاريخ انتهاء العمل
    /// </summary>
    [Column("TerminationDate")]
    public DateTime? TerminationDate { get; set; }

    /// <summary>
    /// العنوان
    /// </summary>
    [StringLength(500)]
    [Column("Address")]
    public string? Address { get; set; }

    /// <summary>
    /// تاريخ الميلاد
    /// </summary>
    [Column("DateOfBirth")]
    public DateTime? DateOfBirth { get; set; }

    /// <summary>
    /// رقم الهوية
    /// </summary>
    [StringLength(20)]
    [Column("IdentityNumber")]
    public string? IdentityNumber { get; set; }

    /// <summary>
    /// الجنسية
    /// </summary>
    [StringLength(100)]
    [Column("Nationality")]
    public string? Nationality { get; set; }

    /// <summary>
    /// الصورة الشخصية
    /// </summary>
    [StringLength(500)]
    [Column("ProfilePicture")]
    public string? ProfilePicture { get; set; }

    /// <summary>
    /// اللغة المفضلة
    /// </summary>
    [StringLength(10)]
    [Column("PreferredLanguage")]
    public string? PreferredLanguage { get; set; } = "ar";

    /// <summary>
    /// المنطقة الزمنية
    /// </summary>
    [StringLength(50)]
    [Column("TimeZone")]
    public string? TimeZone { get; set; } = "Asia/Riyadh";

    /// <summary>
    /// ملاحظات إضافية
    /// </summary>
    [StringLength(2000)]
    [Column("AdditionalNotes")]
    public string? AdditionalNotes { get; set; }

    // Navigation Properties

    /// <summary>
    /// المدير المباشر
    /// </summary>
    [ForeignKey("ManagerId")]
    public virtual User? Manager { get; set; }

    /// <summary>
    /// المرؤوسين
    /// </summary>
    public virtual ICollection<User> Subordinates { get; set; } = new List<User>();

    /// <summary>
    /// صلاحيات المستخدم
    /// </summary>
    public virtual ICollection<UserPermission> UserPermissions { get; set; } = new List<UserPermission>();

    /// <summary>
    /// المبيعات التي قام بها المستخدم
    /// </summary>
    public virtual ICollection<Sale> Sales { get; set; } = new List<Sale>();

    /// <summary>
    /// العملاء المسؤول عنهم
    /// </summary>
    public virtual ICollection<Customer> Customers { get; set; } = new List<Customer>();

    /// <summary>
    /// سجل تسجيل الدخول
    /// </summary>
    public virtual ICollection<UserLoginLog> LoginLogs { get; set; } = new List<UserLoginLog>();

    /// <summary>
    /// سجل العمليات
    /// </summary>
    public virtual ICollection<AuditLog> AuditLogs { get; set; } = new List<AuditLog>();

    // Computed Properties

    /// <summary>
    /// الاسم الكامل
    /// </summary>
    [NotMapped]
    public string FullName => $"{FirstName} {LastName}";

    /// <summary>
    /// هل الحساب مقفل
    /// </summary>
    [NotMapped]
    public bool IsLockedOut => LockoutEnd.HasValue && LockoutEnd.Value > DateTime.Now;

    /// <summary>
    /// العمر
    /// </summary>
    [NotMapped]
    public int? Age => DateOfBirth.HasValue ? DateTime.Now.Year - DateOfBirth.Value.Year : null;

    /// <summary>
    /// سنوات الخبرة
    /// </summary>
    [NotMapped]
    public int? YearsOfExperience => HireDate.HasValue ? DateTime.Now.Year - HireDate.Value.Year : null;

    /// <summary>
    /// إجمالي المبيعات
    /// </summary>
    [NotMapped]
    public decimal TotalSales => Sales?.Where(s => s.Status == SaleStatus.Completed).Sum(s => s.TotalAmount) ?? 0;

    /// <summary>
    /// عدد المبيعات
    /// </summary>
    [NotMapped]
    public int SalesCount => Sales?.Count(s => s.Status == SaleStatus.Completed) ?? 0;

    /// <summary>
    /// العمولة المستحقة
    /// </summary>
    [NotMapped]
    public decimal EarnedCommission => TotalSales * (CommissionRate / 100);

    /// <summary>
    /// عدد أيام منذ آخر تسجيل دخول
    /// </summary>
    [NotMapped]
    public int? DaysSinceLastLogin => LastLoginDate.HasValue ? (DateTime.Now - LastLoginDate.Value).Days : null;

    /// <summary>
    /// هل المستخدم مدير
    /// </summary>
    [NotMapped]
    public bool IsManager => Role == UserRole.Admin || Role == UserRole.SalesManager;

    /// <summary>
    /// هل المستخدم مطور
    /// </summary>
    [NotMapped]
    public bool IsDeveloper => Role == UserRole.Developer;

    /// <summary>
    /// تحديث تاريخ آخر تسجيل دخول
    /// </summary>
    public void UpdateLastLogin()
    {
        LastLoginDate = DateTime.Now;
        FailedLoginAttempts = 0;
    }

    /// <summary>
    /// تسجيل محاولة تسجيل دخول فاشلة
    /// </summary>
    public void RecordFailedLogin()
    {
        FailedLoginAttempts++;
        if (FailedLoginAttempts >= 5) // يمكن جعل هذا قابل للتكوين
        {
            LockoutEnd = DateTime.Now.AddMinutes(15); // قفل لمدة 15 دقيقة
        }
    }

    /// <summary>
    /// إلغاء قفل الحساب
    /// </summary>
    public void UnlockAccount()
    {
        LockoutEnd = null;
        FailedLoginAttempts = 0;
    }

    /// <summary>
    /// تعيين كلمة مرور جديدة
    /// </summary>
    /// <param name="hashedPassword">كلمة المرور المشفرة</param>
    public void SetPassword(string hashedPassword)
    {
        PasswordHash = hashedPassword;
        PasswordChangedDate = DateTime.Now;
        MustChangePassword = false;
        PasswordResetToken = null;
        PasswordResetExpiry = null;
    }
}
