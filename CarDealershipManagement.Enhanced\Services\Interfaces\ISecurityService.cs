namespace CarDealershipManagement.Enhanced.Services.Interfaces;

/// <summary>
/// واجهة خدمة الأمان
/// </summary>
public interface ISecurityService
{
    Task<string> HashPasswordAsync(string password);
    Task<bool> VerifyPasswordAsync(string password, string hash);
    Task<string> GenerateSecureTokenAsync();
    Task<string> EncryptDataAsync(string data);
    Task<string> DecryptDataAsync(string encryptedData);
    Task<bool> ValidatePasswordStrengthAsync(string password);
    Task<string> GenerateRandomPasswordAsync(int length = 12);
    Task<bool> IsAccountLockedAsync(int userId);
    Task<bool> ShouldLockAccountAsync(int userId);
    Task LogSecurityEventAsync(string eventType, string description, int? userId = null);
}
