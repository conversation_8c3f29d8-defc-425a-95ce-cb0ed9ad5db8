using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using CarDealershipManagement.Enhanced.Enums;

namespace CarDealershipManagement.Enhanced.Models;

/// <summary>
/// نموذج دفعة القسط
/// </summary>
[Table("InstallmentPayments")]
public class InstallmentPayment : BaseEntity
{
    /// <summary>
    /// معرف البيع
    /// </summary>
    [Required(ErrorMessage = "البيع مطلوب")]
    [Column("SaleId")]
    public int SaleId { get; set; }

    /// <summary>
    /// معرف العميل
    /// </summary>
    [Required(ErrorMessage = "العميل مطلوب")]
    [Column("CustomerId")]
    public int CustomerId { get; set; }

    /// <summary>
    /// رقم القسط
    /// </summary>
    [Required(ErrorMessage = "رقم القسط مطلوب")]
    [Range(1, int.MaxValue, ErrorMessage = "رقم القسط يجب أن يكون أكبر من صفر")]
    [Column("InstallmentNumber")]
    public int InstallmentNumber { get; set; }

    /// <summary>
    /// تاريخ الاستحقاق
    /// </summary>
    [Required(ErrorMessage = "تاريخ الاستحقاق مطلوب")]
    [Column("DueDate")]
    public DateTime DueDate { get; set; }

    /// <summary>
    /// المبلغ المستحق
    /// </summary>
    [Required(ErrorMessage = "المبلغ المستحق مطلوب")]
    [Range(0, double.MaxValue, ErrorMessage = "المبلغ المستحق يجب أن يكون أكبر من صفر")]
    [Column("DueAmount", TypeName = "decimal(18,2)")]
    public decimal DueAmount { get; set; }

    /// <summary>
    /// المبلغ المدفوع
    /// </summary>
    [Range(0, double.MaxValue, ErrorMessage = "المبلغ المدفوع يجب أن يكون أكبر من أو يساوي صفر")]
    [Column("PaidAmount", TypeName = "decimal(18,2)")]
    public decimal PaidAmount { get; set; } = 0;

    /// <summary>
    /// المبلغ المتبقي
    /// </summary>
    [Column("RemainingAmount", TypeName = "decimal(18,2)")]
    public decimal RemainingAmount { get; set; }

    /// <summary>
    /// تاريخ الدفع
    /// </summary>
    [Column("PaymentDate")]
    public DateTime? PaymentDate { get; set; }

    /// <summary>
    /// نوع الدفع
    /// </summary>
    [Column("PaymentType")]
    public PaymentType? PaymentType { get; set; }

    /// <summary>
    /// حالة القسط
    /// </summary>
    [Required]
    [Column("Status")]
    public InstallmentStatus Status { get; set; } = InstallmentStatus.Pending;

    /// <summary>
    /// رقم الإيصال
    /// </summary>
    [StringLength(100)]
    [Column("ReceiptNumber")]
    public string? ReceiptNumber { get; set; }

    /// <summary>
    /// رقم الشيك (في حالة الدفع بالشيك)
    /// </summary>
    [StringLength(50)]
    [Column("CheckNumber")]
    public string? CheckNumber { get; set; }

    /// <summary>
    /// اسم البنك (في حالة الدفع بالشيك أو التحويل)
    /// </summary>
    [StringLength(200)]
    [Column("BankName")]
    public string? BankName { get; set; }

    /// <summary>
    /// رقم المرجع (في حالة التحويل البنكي)
    /// </summary>
    [StringLength(100)]
    [Column("ReferenceNumber")]
    public string? ReferenceNumber { get; set; }

    /// <summary>
    /// غرامة التأخير
    /// </summary>
    [Range(0, double.MaxValue, ErrorMessage = "غرامة التأخير يجب أن تكون أكبر من أو تساوي صفر")]
    [Column("LateFee", TypeName = "decimal(18,2)")]
    public decimal LateFee { get; set; } = 0;

    /// <summary>
    /// عدد أيام التأخير
    /// </summary>
    [Column("DaysLate")]
    public int DaysLate { get; set; } = 0;

    /// <summary>
    /// معرف المستخدم الذي سجل الدفع
    /// </summary>
    [Column("RecordedBy")]
    public int? RecordedBy { get; set; }

    /// <summary>
    /// ملاحظات الدفع
    /// </summary>
    [StringLength(1000)]
    [Column("PaymentNotes")]
    public string? PaymentNotes { get; set; }

    /// <summary>
    /// هل تم إرسال تذكير
    /// </summary>
    [Column("ReminderSent")]
    public bool ReminderSent { get; set; } = false;

    /// <summary>
    /// تاريخ آخر تذكير
    /// </summary>
    [Column("LastReminderDate")]
    public DateTime? LastReminderDate { get; set; }

    /// <summary>
    /// عدد التذكيرات المرسلة
    /// </summary>
    [Column("ReminderCount")]
    public int ReminderCount { get; set; } = 0;

    /// <summary>
    /// تاريخ التأكيد
    /// </summary>
    [Column("ConfirmationDate")]
    public DateTime? ConfirmationDate { get; set; }

    /// <summary>
    /// معرف المستخدم الذي أكد الدفع
    /// </summary>
    [Column("ConfirmedBy")]
    public int? ConfirmedBy { get; set; }

    // Navigation Properties

    /// <summary>
    /// البيع المرتبط بالقسط
    /// </summary>
    [ForeignKey("SaleId")]
    public virtual Sale Sale { get; set; } = null!;

    /// <summary>
    /// العميل
    /// </summary>
    [ForeignKey("CustomerId")]
    public virtual Customer Customer { get; set; } = null!;

    /// <summary>
    /// المستخدم الذي سجل الدفع
    /// </summary>
    [ForeignKey("RecordedBy")]
    public virtual User? RecordedByUser { get; set; }

    /// <summary>
    /// المستخدم الذي أكد الدفع
    /// </summary>
    [ForeignKey("ConfirmedBy")]
    public virtual User? ConfirmedByUser { get; set; }

    // Computed Properties

    /// <summary>
    /// هل القسط مدفوع بالكامل
    /// </summary>
    [NotMapped]
    public bool IsFullyPaid => RemainingAmount <= 0;

    /// <summary>
    /// هل القسط متأخر
    /// </summary>
    [NotMapped]
    public bool IsOverdue => Status == InstallmentStatus.Overdue || (Status == InstallmentStatus.Pending && DueDate < DateTime.Now.Date);

    /// <summary>
    /// عدد أيام التأخير الحالي
    /// </summary>
    [NotMapped]
    public int CurrentDaysLate => IsOverdue && Status == InstallmentStatus.Pending ? (DateTime.Now.Date - DueDate).Days : DaysLate;

    /// <summary>
    /// نسبة الدفع
    /// </summary>
    [NotMapped]
    public decimal PaymentPercentage => DueAmount > 0 ? (PaidAmount / DueAmount) * 100 : 0;

    /// <summary>
    /// المبلغ الإجمالي مع الغرامة
    /// </summary>
    [NotMapped]
    public decimal TotalAmountWithFee => DueAmount + LateFee;

    /// <summary>
    /// هل يحتاج تذكير
    /// </summary>
    [NotMapped]
    public bool NeedsReminder => Status == InstallmentStatus.Pending && 
                                DueDate.AddDays(-3) <= DateTime.Now.Date && 
                                (!LastReminderDate.HasValue || LastReminderDate.Value.Date < DateTime.Now.Date);

    /// <summary>
    /// حالة القسط كنص
    /// </summary>
    [NotMapped]
    public string StatusText => Status switch
    {
        InstallmentStatus.Pending => "معلق",
        InstallmentStatus.Paid => "مدفوع",
        InstallmentStatus.Overdue => "متأخر",
        InstallmentStatus.PartiallyPaid => "مدفوع جزئياً",
        InstallmentStatus.Cancelled => "ملغي",
        _ => "غير محدد"
    };

    /// <summary>
    /// تحديث حالة القسط بناءً على المبلغ المدفوع
    /// </summary>
    public void UpdateStatus()
    {
        RemainingAmount = DueAmount - PaidAmount;

        if (RemainingAmount <= 0)
        {
            Status = InstallmentStatus.Paid;
            RemainingAmount = 0;
        }
        else if (PaidAmount > 0)
        {
            Status = InstallmentStatus.PartiallyPaid;
        }
        else if (DueDate < DateTime.Now.Date)
        {
            Status = InstallmentStatus.Overdue;
            DaysLate = (DateTime.Now.Date - DueDate).Days;
        }
        else
        {
            Status = InstallmentStatus.Pending;
        }
    }

    /// <summary>
    /// تسجيل دفعة
    /// </summary>
    /// <param name="amount">المبلغ المدفوع</param>
    /// <param name="paymentType">نوع الدفع</param>
    /// <param name="userId">معرف المستخدم</param>
    /// <param name="notes">ملاحظات</param>
    public void RecordPayment(decimal amount, PaymentType paymentType, int? userId = null, string? notes = null)
    {
        PaidAmount += amount;
        PaymentDate = DateTime.Now;
        PaymentType = paymentType;
        RecordedBy = userId;
        PaymentNotes = notes;
        
        UpdateStatus();
        UpdateModificationInfo(userId);
    }

    /// <summary>
    /// حساب غرامة التأخير
    /// </summary>
    /// <param name="dailyFeeRate">معدل الغرامة اليومية</param>
    public void CalculateLateFee(decimal dailyFeeRate = 0.001m) // 0.1% يومياً افتراضياً
    {
        if (IsOverdue && Status != InstallmentStatus.Paid)
        {
            var daysLate = CurrentDaysLate;
            LateFee = DueAmount * dailyFeeRate * daysLate;
            DaysLate = daysLate;
        }
    }
}
