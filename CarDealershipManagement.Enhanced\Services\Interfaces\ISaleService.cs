using CarDealershipManagement.Enhanced.Models;
using CarDealershipManagement.Enhanced.Enums;

namespace CarDealershipManagement.Enhanced.Services.Interfaces;

/// <summary>
/// واجهة خدمة المبيعات
/// </summary>
public interface ISaleService : IBaseService<Sale>
{
    /// <summary>
    /// البحث في المبيعات برقم البيع
    /// </summary>
    /// <param name="saleNumber">رقم البيع</param>
    /// <returns>البيع أو null</returns>
    Task<Sale?> GetBySaleNumberAsync(string saleNumber);

    /// <summary>
    /// الحصول على المبيعات حسب الحالة
    /// </summary>
    /// <param name="status">الحالة</param>
    /// <returns>قائمة المبيعات</returns>
    Task<IEnumerable<Sale>> GetByStatusAsync(SaleStatus status);

    /// <summary>
    /// الحصول على المبيعات حسب العميل
    /// </summary>
    /// <param name="customerId">معرف العميل</param>
    /// <returns>قائمة المبيعات</returns>
    Task<IEnumerable<Sale>> GetByCustomerAsync(int customerId);

    /// <summary>
    /// الحصول على المبيعات حسب مندوب المبيعات
    /// </summary>
    /// <param name="salesAgentId">معرف مندوب المبيعات</param>
    /// <returns>قائمة المبيعات</returns>
    Task<IEnumerable<Sale>> GetBySalesAgentAsync(int salesAgentId);

    /// <summary>
    /// الحصول على المبيعات في فترة زمنية
    /// </summary>
    /// <param name="startDate">تاريخ البداية</param>
    /// <param name="endDate">تاريخ النهاية</param>
    /// <returns>قائمة المبيعات</returns>
    Task<IEnumerable<Sale>> GetByDateRangeAsync(DateTime startDate, DateTime endDate);

    /// <summary>
    /// إنشاء بيع جديد
    /// </summary>
    /// <param name="carId">معرف السيارة</param>
    /// <param name="customerId">معرف العميل</param>
    /// <param name="salePrice">سعر البيع</param>
    /// <param name="paymentType">نوع الدفع</param>
    /// <param name="salesAgentId">معرف مندوب المبيعات</param>
    /// <param name="userId">معرف المستخدم</param>
    /// <returns>البيع المنشأ</returns>
    Task<Sale> CreateSaleAsync(
        int carId,
        int customerId,
        decimal salePrice,
        PaymentType paymentType,
        int salesAgentId,
        int? userId = null);

    /// <summary>
    /// تأكيد البيع
    /// </summary>
    /// <param name="saleId">معرف البيع</param>
    /// <param name="userId">معرف المستخدم</param>
    /// <returns>true إذا تم التأكيد بنجاح</returns>
    Task<bool> ConfirmSaleAsync(int saleId, int? userId = null);

    /// <summary>
    /// إلغاء البيع
    /// </summary>
    /// <param name="saleId">معرف البيع</param>
    /// <param name="reason">سبب الإلغاء</param>
    /// <param name="userId">معرف المستخدم</param>
    /// <returns>true إذا تم الإلغاء بنجاح</returns>
    Task<bool> CancelSaleAsync(int saleId, string reason, int? userId = null);

    /// <summary>
    /// تحديث حالة البيع
    /// </summary>
    /// <param name="saleId">معرف البيع</param>
    /// <param name="status">الحالة الجديدة</param>
    /// <param name="userId">معرف المستخدم</param>
    /// <returns>true إذا تم التحديث بنجاح</returns>
    Task<bool> UpdateSaleStatusAsync(int saleId, SaleStatus status, int? userId = null);

    /// <summary>
    /// إضافة دفعة للبيع
    /// </summary>
    /// <param name="saleId">معرف البيع</param>
    /// <param name="amount">المبلغ</param>
    /// <param name="paymentType">نوع الدفع</param>
    /// <param name="userId">معرف المستخدم</param>
    /// <returns>true إذا تم إضافة الدفعة بنجاح</returns>
    Task<bool> AddPaymentAsync(int saleId, decimal amount, PaymentType paymentType, int? userId = null);

    /// <summary>
    /// إنشاء جدول الأقساط
    /// </summary>
    /// <param name="saleId">معرف البيع</param>
    /// <param name="numberOfInstallments">عدد الأقساط</param>
    /// <param name="firstInstallmentDate">تاريخ أول قسط</param>
    /// <param name="userId">معرف المستخدم</param>
    /// <returns>قائمة الأقساط المنشأة</returns>
    Task<IEnumerable<InstallmentPayment>> CreateInstallmentScheduleAsync(
        int saleId,
        int numberOfInstallments,
        DateTime firstInstallmentDate,
        int? userId = null);

    /// <summary>
    /// الحصول على أقساط البيع
    /// </summary>
    /// <param name="saleId">معرف البيع</param>
    /// <returns>قائمة الأقساط</returns>
    Task<IEnumerable<InstallmentPayment>> GetSaleInstallmentsAsync(int saleId);

    /// <summary>
    /// الحصول على الأقساط المستحقة للبيع
    /// </summary>
    /// <param name="saleId">معرف البيع</param>
    /// <returns>قائمة الأقساط المستحقة</returns>
    Task<IEnumerable<InstallmentPayment>> GetOverdueInstallmentsAsync(int saleId);

    /// <summary>
    /// حساب إجمالي الربح للبيع
    /// </summary>
    /// <param name="saleId">معرف البيع</param>
    /// <returns>إجمالي الربح</returns>
    Task<decimal> CalculateSaleProfitAsync(int saleId);

    /// <summary>
    /// حساب عمولة مندوب المبيعات
    /// </summary>
    /// <param name="saleId">معرف البيع</param>
    /// <returns>قيمة العمولة</returns>
    Task<decimal> CalculateSalesCommissionAsync(int saleId);

    /// <summary>
    /// إضافة مستند للبيع
    /// </summary>
    /// <param name="saleId">معرف البيع</param>
    /// <param name="document">بيانات المستند</param>
    /// <param name="userId">معرف المستخدم</param>
    /// <returns>المستند المضاف</returns>
    Task<SaleDocument> AddSaleDocumentAsync(int saleId, SaleDocument document, int? userId = null);

    /// <summary>
    /// الحصول على مستندات البيع
    /// </summary>
    /// <param name="saleId">معرف البيع</param>
    /// <returns>قائمة المستندات</returns>
    Task<IEnumerable<SaleDocument>> GetSaleDocumentsAsync(int saleId);

    /// <summary>
    /// الحصول على إحصائيات المبيعات
    /// </summary>
    /// <param name="startDate">تاريخ البداية</param>
    /// <param name="endDate">تاريخ النهاية</param>
    /// <returns>إحصائيات المبيعات</returns>
    Task<SalesStatistics> GetSalesStatisticsAsync(DateTime? startDate = null, DateTime? endDate = null);

    /// <summary>
    /// الحصول على المبيعات المعلقة
    /// </summary>
    /// <returns>قائمة المبيعات المعلقة</returns>
    Task<IEnumerable<Sale>> GetPendingSalesAsync();

    /// <summary>
    /// الحصول على أفضل مندوبي المبيعات
    /// </summary>
    /// <param name="startDate">تاريخ البداية</param>
    /// <param name="endDate">تاريخ النهاية</param>
    /// <param name="count">عدد المندوبين</param>
    /// <returns>قائمة أفضل مندوبي المبيعات</returns>
    Task<IEnumerable<SalesAgentPerformance>> GetTopSalesAgentsAsync(
        DateTime? startDate = null,
        DateTime? endDate = null,
        int count = 10);

    /// <summary>
    /// توليد رقم بيع جديد
    /// </summary>
    /// <returns>رقم البيع الجديد</returns>
    Task<string> GenerateSaleNumberAsync();

    /// <summary>
    /// التحقق من إمكانية بيع السيارة
    /// </summary>
    /// <param name="carId">معرف السيارة</param>
    /// <returns>true إذا كان بالإمكان بيع السيارة</returns>
    Task<bool> CanSellCarAsync(int carId);

    /// <summary>
    /// التحقق من الحد الائتماني للعميل
    /// </summary>
    /// <param name="customerId">معرف العميل</param>
    /// <param name="saleAmount">مبلغ البيع</param>
    /// <returns>true إذا كان العميل يستطيع الشراء</returns>
    Task<bool> CheckCustomerCreditLimitAsync(int customerId, decimal saleAmount);
}

/// <summary>
/// إحصائيات المبيعات
/// </summary>
public class SalesStatistics
{
    /// <summary>
    /// العدد الإجمالي للمبيعات
    /// </summary>
    public int TotalSales { get; set; }

    /// <summary>
    /// إجمالي قيمة المبيعات
    /// </summary>
    public decimal TotalSalesValue { get; set; }

    /// <summary>
    /// إجمالي الربح
    /// </summary>
    public decimal TotalProfit { get; set; }

    /// <summary>
    /// متوسط قيمة البيع
    /// </summary>
    public decimal AverageSaleValue { get; set; }

    /// <summary>
    /// أعلى قيمة بيع
    /// </summary>
    public decimal HighestSaleValue { get; set; }

    /// <summary>
    /// عدد المبيعات النقدية
    /// </summary>
    public int CashSales { get; set; }

    /// <summary>
    /// عدد المبيعات بالتقسيط
    /// </summary>
    public int InstallmentSales { get; set; }

    /// <summary>
    /// عدد المبيعات المؤكدة
    /// </summary>
    public int ConfirmedSales { get; set; }

    /// <summary>
    /// عدد المبيعات المعلقة
    /// </summary>
    public int PendingSales { get; set; }

    /// <summary>
    /// عدد المبيعات الملغاة
    /// </summary>
    public int CancelledSales { get; set; }

    /// <summary>
    /// إجمالي العمولات
    /// </summary>
    public decimal TotalCommissions { get; set; }
}

/// <summary>
/// أداء مندوب المبيعات
/// </summary>
public class SalesAgentPerformance
{
    /// <summary>
    /// معرف مندوب المبيعات
    /// </summary>
    public int SalesAgentId { get; set; }

    /// <summary>
    /// اسم مندوب المبيعات
    /// </summary>
    public string SalesAgentName { get; set; } = string.Empty;

    /// <summary>
    /// عدد المبيعات
    /// </summary>
    public int SalesCount { get; set; }

    /// <summary>
    /// إجمالي قيمة المبيعات
    /// </summary>
    public decimal TotalSalesValue { get; set; }

    /// <summary>
    /// إجمالي العمولات
    /// </summary>
    public decimal TotalCommissions { get; set; }

    /// <summary>
    /// متوسط قيمة البيع
    /// </summary>
    public decimal AverageSaleValue { get; set; }
}
