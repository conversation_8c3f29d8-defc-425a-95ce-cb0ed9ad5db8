using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using CarDealershipManagement.Enhanced.Enums;

namespace CarDealershipManagement.Enhanced.Models;

/// <summary>
/// الكلاس الأساسي للمستندات
/// </summary>
public abstract class BaseDocument : BaseEntity
{
    /// <summary>
    /// اسم الملف
    /// </summary>
    [Required(ErrorMessage = "اسم الملف مطلوب")]
    [StringLength(255, ErrorMessage = "اسم الملف يجب أن يكون أقل من 255 حرف")]
    [Column("FileName")]
    public string FileName { get; set; } = string.Empty;

    /// <summary>
    /// المسار الأصلي للملف
    /// </summary>
    [StringLength(255)]
    [Column("OriginalFileName")]
    public string? OriginalFileName { get; set; }

    /// <summary>
    /// مسار الملف
    /// </summary>
    [Required(ErrorMessage = "مسار الملف مطلوب")]
    [StringLength(500, ErrorMessage = "مسار الملف يجب أن يكون أقل من 500 حرف")]
    [Column("FilePath")]
    public string FilePath { get; set; } = string.Empty;

    /// <summary>
    /// نوع الملف
    /// </summary>
    [StringLength(50)]
    [Column("FileType")]
    public string? FileType { get; set; }

    /// <summary>
    /// حجم الملف بالبايت
    /// </summary>
    [Range(0, long.MaxValue, ErrorMessage = "حجم الملف يجب أن يكون أكبر من أو يساوي صفر")]
    [Column("FileSize")]
    public long FileSize { get; set; }

    /// <summary>
    /// نوع المستند
    /// </summary>
    [Required]
    [Column("DocumentType")]
    public DocumentType DocumentType { get; set; }

    /// <summary>
    /// وصف المستند
    /// </summary>
    [StringLength(500)]
    [Column("Description")]
    public string? Description { get; set; }

    /// <summary>
    /// تاريخ انتهاء الصلاحية
    /// </summary>
    [Column("ExpiryDate")]
    public DateTime? ExpiryDate { get; set; }

    /// <summary>
    /// هل المستند مؤرشف
    /// </summary>
    [Column("IsArchived")]
    public bool IsArchived { get; set; } = false;

    /// <summary>
    /// تاريخ الأرشفة
    /// </summary>
    [Column("ArchivedDate")]
    public DateTime? ArchivedDate { get; set; }

    /// <summary>
    /// معرف المستخدم الذي أرشف المستند
    /// </summary>
    [Column("ArchivedBy")]
    public int? ArchivedBy { get; set; }

    // Computed Properties

    /// <summary>
    /// حجم الملف بصيغة قابلة للقراءة
    /// </summary>
    [NotMapped]
    public string FileSizeFormatted
    {
        get
        {
            string[] sizes = { "B", "KB", "MB", "GB", "TB" };
            double len = FileSize;
            int order = 0;
            while (len >= 1024 && order < sizes.Length - 1)
            {
                order++;
                len = len / 1024;
            }
            return $"{len:0.##} {sizes[order]}";
        }
    }

    /// <summary>
    /// هل المستند منتهي الصلاحية
    /// </summary>
    [NotMapped]
    public bool IsExpired => ExpiryDate.HasValue && ExpiryDate.Value < DateTime.Now;

    /// <summary>
    /// عدد أيام حتى انتهاء الصلاحية
    /// </summary>
    [NotMapped]
    public int? DaysUntilExpiry => ExpiryDate.HasValue ? (ExpiryDate.Value - DateTime.Now).Days : null;

    /// <summary>
    /// هل المستند يحتاج تجديد قريباً (خلال 30 يوم)
    /// </summary>
    [NotMapped]
    public bool NeedsRenewal => DaysUntilExpiry.HasValue && DaysUntilExpiry.Value <= 30 && DaysUntilExpiry.Value > 0;
}

/// <summary>
/// مستندات السيارة
/// </summary>
[Table("CarDocuments")]
public class CarDocument : BaseDocument
{
    /// <summary>
    /// معرف السيارة
    /// </summary>
    [Required(ErrorMessage = "السيارة مطلوبة")]
    [Column("CarId")]
    public int CarId { get; set; }

    /// <summary>
    /// السيارة
    /// </summary>
    [ForeignKey("CarId")]
    public virtual Car Car { get; set; } = null!;
}

/// <summary>
/// مستندات العميل
/// </summary>
[Table("CustomerDocuments")]
public class CustomerDocument : BaseDocument
{
    /// <summary>
    /// معرف العميل
    /// </summary>
    [Required(ErrorMessage = "العميل مطلوب")]
    [Column("CustomerId")]
    public int CustomerId { get; set; }

    /// <summary>
    /// العميل
    /// </summary>
    [ForeignKey("CustomerId")]
    public virtual Customer Customer { get; set; } = null!;
}

/// <summary>
/// مستندات المورد
/// </summary>
[Table("SupplierDocuments")]
public class SupplierDocument : BaseDocument
{
    /// <summary>
    /// معرف المورد
    /// </summary>
    [Required(ErrorMessage = "المورد مطلوب")]
    [Column("SupplierId")]
    public int SupplierId { get; set; }

    /// <summary>
    /// المورد
    /// </summary>
    [ForeignKey("SupplierId")]
    public virtual Supplier Supplier { get; set; } = null!;
}

/// <summary>
/// مستندات البيع
/// </summary>
[Table("SaleDocuments")]
public class SaleDocument : BaseDocument
{
    /// <summary>
    /// معرف البيع
    /// </summary>
    [Required(ErrorMessage = "البيع مطلوب")]
    [Column("SaleId")]
    public int SaleId { get; set; }

    /// <summary>
    /// البيع
    /// </summary>
    [ForeignKey("SaleId")]
    public virtual Sale Sale { get; set; } = null!;
}

/// <summary>
/// صور السيارة
/// </summary>
[Table("CarImages")]
public class CarImage : BaseEntity
{
    /// <summary>
    /// معرف السيارة
    /// </summary>
    [Required(ErrorMessage = "السيارة مطلوبة")]
    [Column("CarId")]
    public int CarId { get; set; }

    /// <summary>
    /// اسم الملف
    /// </summary>
    [Required(ErrorMessage = "اسم الملف مطلوب")]
    [StringLength(255, ErrorMessage = "اسم الملف يجب أن يكون أقل من 255 حرف")]
    [Column("FileName")]
    public string FileName { get; set; } = string.Empty;

    /// <summary>
    /// مسار الملف
    /// </summary>
    [Required(ErrorMessage = "مسار الملف مطلوب")]
    [StringLength(500, ErrorMessage = "مسار الملف يجب أن يكون أقل من 500 حرف")]
    [Column("FilePath")]
    public string FilePath { get; set; } = string.Empty;

    /// <summary>
    /// حجم الملف بالبايت
    /// </summary>
    [Range(0, long.MaxValue, ErrorMessage = "حجم الملف يجب أن يكون أكبر من أو يساوي صفر")]
    [Column("FileSize")]
    public long FileSize { get; set; }

    /// <summary>
    /// وصف الصورة
    /// </summary>
    [StringLength(500)]
    [Column("Description")]
    public string? Description { get; set; }

    /// <summary>
    /// هل الصورة رئيسية
    /// </summary>
    [Column("IsPrimary")]
    public bool IsPrimary { get; set; } = false;

    /// <summary>
    /// ترتيب الصورة
    /// </summary>
    [Column("SortOrder")]
    public int SortOrder { get; set; } = 0;

    /// <summary>
    /// مسار الصورة المصغرة
    /// </summary>
    [StringLength(500)]
    [Column("ThumbnailPath")]
    public string? ThumbnailPath { get; set; }

    /// <summary>
    /// عرض الصورة بالبكسل
    /// </summary>
    [Column("Width")]
    public int? Width { get; set; }

    /// <summary>
    /// ارتفاع الصورة بالبكسل
    /// </summary>
    [Column("Height")]
    public int? Height { get; set; }

    /// <summary>
    /// السيارة
    /// </summary>
    [ForeignKey("CarId")]
    public virtual Car Car { get; set; } = null!;

    // Computed Properties

    /// <summary>
    /// حجم الملف بصيغة قابلة للقراءة
    /// </summary>
    [NotMapped]
    public string FileSizeFormatted
    {
        get
        {
            string[] sizes = { "B", "KB", "MB", "GB" };
            double len = FileSize;
            int order = 0;
            while (len >= 1024 && order < sizes.Length - 1)
            {
                order++;
                len = len / 1024;
            }
            return $"{len:0.##} {sizes[order]}";
        }
    }

    /// <summary>
    /// نسبة العرض إلى الارتفاع
    /// </summary>
    [NotMapped]
    public double? AspectRatio => Width.HasValue && Height.HasValue && Height.Value > 0 ? (double)Width.Value / Height.Value : null;
}
