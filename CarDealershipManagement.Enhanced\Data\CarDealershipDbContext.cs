using Microsoft.EntityFrameworkCore;
using CarDealershipManagement.Enhanced.Models;
using CarDealershipManagement.Enhanced.Enums;

namespace CarDealershipManagement.Enhanced.Data;

/// <summary>
/// سياق قاعدة البيانات المحسن لنظام إدارة معرض السيارات
/// </summary>
public class CarDealershipDbContext : DbContext
{
    public CarDealershipDbContext(DbContextOptions<CarDealershipDbContext> options) : base(options)
    {
    }

    // DbSets for all entities
    public DbSet<Car> Cars { get; set; }
    public DbSet<Customer> Customers { get; set; }
    public DbSet<Supplier> Suppliers { get; set; }
    public DbSet<Sale> Sales { get; set; }
    public DbSet<User> Users { get; set; }
    public DbSet<InstallmentPayment> InstallmentPayments { get; set; }
    public DbSet<CarDocument> CarDocuments { get; set; }
    public DbSet<CarImage> CarImages { get; set; }
    public DbSet<CustomerDocument> CustomerDocuments { get; set; }
    public DbSet<SupplierDocument> SupplierDocuments { get; set; }
    public DbSet<SaleDocument> SaleDocuments { get; set; }
    public DbSet<UserPermission> UserPermissions { get; set; }
    public DbSet<UserLoginLog> UserLoginLogs { get; set; }
    public DbSet<AuditLog> AuditLogs { get; set; }
    public DbSet<SystemSetting> SystemSettings { get; set; }
    public DbSet<AuthorizedInstallation> AuthorizedInstallations { get; set; }
    public DbSet<SupplierPayment> SupplierPayments { get; set; }
    public DbSet<MaintenanceRecord> MaintenanceRecords { get; set; }
    public DbSet<CustomerInteraction> CustomerInteractions { get; set; }
    public DbSet<SupplierContract> SupplierContracts { get; set; }
    public DbSet<BackupRecord> BackupRecords { get; set; }

    protected override void OnModelCreating(ModelBuilder modelBuilder)
    {
        base.OnModelCreating(modelBuilder);

        // Configure entity relationships and constraints
        ConfigureCarEntity(modelBuilder);
        ConfigureCustomerEntity(modelBuilder);
        ConfigureSupplierEntity(modelBuilder);
        ConfigureSaleEntity(modelBuilder);
        ConfigureUserEntity(modelBuilder);
        ConfigureInstallmentPaymentEntity(modelBuilder);
        ConfigureDocumentEntities(modelBuilder);
        ConfigureSupportEntities(modelBuilder);
        ConfigureIndexes(modelBuilder);
        ConfigureEnumConversions(modelBuilder);
        ConfigureGlobalFilters(modelBuilder);
        SeedData(modelBuilder);
    }

    private void ConfigureCarEntity(ModelBuilder modelBuilder)
    {
        modelBuilder.Entity<Car>(entity =>
        {
            entity.HasIndex(e => e.ChassisNumber).IsUnique();
            entity.Property(e => e.ChassisNumber).IsRequired();
            entity.Property(e => e.Brand).IsRequired();
            entity.Property(e => e.Model).IsRequired();
            entity.Property(e => e.Color).IsRequired();
            entity.Property(e => e.PurchasePrice).HasPrecision(18, 2);
            entity.Property(e => e.SalePrice).HasPrecision(18, 2);

            // Relationships
            entity.HasOne(e => e.Supplier)
                  .WithMany(s => s.Cars)
                  .HasForeignKey(e => e.SupplierId)
                  .OnDelete(DeleteBehavior.SetNull);

            entity.HasOne(e => e.Customer)
                  .WithMany(c => c.Cars)
                  .HasForeignKey(e => e.CustomerId)
                  .OnDelete(DeleteBehavior.SetNull);
        });
    }

    private void ConfigureCustomerEntity(ModelBuilder modelBuilder)
    {
        modelBuilder.Entity<Customer>(entity =>
        {
            entity.HasIndex(e => e.IdentityNumber).IsUnique();
            entity.HasIndex(e => e.Phone);
            entity.HasIndex(e => e.Email);
            entity.Property(e => e.FirstName).IsRequired();
            entity.Property(e => e.LastName).IsRequired();
            entity.Property(e => e.IdentityNumber).IsRequired();
            entity.Property(e => e.Phone).IsRequired();
            entity.Property(e => e.MonthlySalary).HasPrecision(18, 2);
            entity.Property(e => e.CreditLimit).HasPrecision(18, 2);
            entity.Property(e => e.CurrentBalance).HasPrecision(18, 2);

            // Relationships
            entity.HasOne(e => e.SalesAgent)
                  .WithMany(u => u.Customers)
                  .HasForeignKey(e => e.SalesAgentId)
                  .OnDelete(DeleteBehavior.SetNull);
        });
    }

    private void ConfigureSupplierEntity(ModelBuilder modelBuilder)
    {
        modelBuilder.Entity<Supplier>(entity =>
        {
            entity.HasIndex(e => e.CompanyName);
            entity.HasIndex(e => e.Phone);
            entity.HasIndex(e => e.Email);
            entity.Property(e => e.CompanyName).IsRequired();
            entity.Property(e => e.ContactName).IsRequired();
            entity.Property(e => e.Phone).IsRequired();
            entity.Property(e => e.CreditLimit).HasPrecision(18, 2);
            entity.Property(e => e.CurrentBalance).HasPrecision(18, 2);
        });
    }

    private void ConfigureSaleEntity(ModelBuilder modelBuilder)
    {
        modelBuilder.Entity<Sale>(entity =>
        {
            entity.HasIndex(e => e.SaleNumber).IsUnique();
            entity.HasIndex(e => e.SaleDate);
            entity.Property(e => e.SaleNumber).IsRequired();
            entity.Property(e => e.SalePrice).HasPrecision(18, 2);
            entity.Property(e => e.Discount).HasPrecision(18, 2);
            entity.Property(e => e.Tax).HasPrecision(18, 2);
            entity.Property(e => e.AdditionalFees).HasPrecision(18, 2);
            entity.Property(e => e.TotalAmount).HasPrecision(18, 2);
            entity.Property(e => e.PaidAmount).HasPrecision(18, 2);
            entity.Property(e => e.RemainingAmount).HasPrecision(18, 2);
            entity.Property(e => e.MonthlyInstallment).HasPrecision(18, 2);
            entity.Property(e => e.InterestRate).HasPrecision(5, 2);

            // Relationships
            entity.HasOne(e => e.Car)
                  .WithMany(c => c.Sales)
                  .HasForeignKey(e => e.CarId)
                  .OnDelete(DeleteBehavior.Restrict);

            entity.HasOne(e => e.Customer)
                  .WithMany(c => c.Sales)
                  .HasForeignKey(e => e.CustomerId)
                  .OnDelete(DeleteBehavior.Restrict);

            entity.HasOne(e => e.SalesAgent)
                  .WithMany(u => u.Sales)
                  .HasForeignKey(e => e.SalesAgentId)
                  .OnDelete(DeleteBehavior.Restrict);

            entity.HasOne(e => e.ConfirmedByUser)
                  .WithMany()
                  .HasForeignKey(e => e.ConfirmedBy)
                  .OnDelete(DeleteBehavior.SetNull);
        });
    }

    private void ConfigureUserEntity(ModelBuilder modelBuilder)
    {
        modelBuilder.Entity<User>(entity =>
        {
            entity.HasIndex(e => e.Username).IsUnique();
            entity.HasIndex(e => e.Email);
            entity.Property(e => e.Username).IsRequired();
            entity.Property(e => e.PasswordHash).IsRequired();
            entity.Property(e => e.FirstName).IsRequired();
            entity.Property(e => e.LastName).IsRequired();
            entity.Property(e => e.Salary).HasPrecision(18, 2);
            entity.Property(e => e.CommissionRate).HasPrecision(5, 2);

            // Self-referencing relationship for Manager
            entity.HasOne(e => e.Manager)
                  .WithMany(u => u.Subordinates)
                  .HasForeignKey(e => e.ManagerId)
                  .OnDelete(DeleteBehavior.SetNull);
        });
    }

    private void ConfigureInstallmentPaymentEntity(ModelBuilder modelBuilder)
    {
        modelBuilder.Entity<InstallmentPayment>(entity =>
        {
            entity.HasIndex(e => new { e.SaleId, e.InstallmentNumber }).IsUnique();
            entity.HasIndex(e => e.DueDate);
            entity.Property(e => e.DueAmount).HasPrecision(18, 2);
            entity.Property(e => e.PaidAmount).HasPrecision(18, 2);
            entity.Property(e => e.RemainingAmount).HasPrecision(18, 2);
            entity.Property(e => e.LateFee).HasPrecision(18, 2);

            // Relationships
            entity.HasOne(e => e.Sale)
                  .WithMany(s => s.InstallmentPayments)
                  .HasForeignKey(e => e.SaleId)
                  .OnDelete(DeleteBehavior.Cascade);

            entity.HasOne(e => e.Customer)
                  .WithMany(c => c.InstallmentPayments)
                  .HasForeignKey(e => e.CustomerId)
                  .OnDelete(DeleteBehavior.Restrict);
        });
    }

    private void ConfigureDocumentEntities(ModelBuilder modelBuilder)
    {
        // Car Documents
        modelBuilder.Entity<CarDocument>(entity =>
        {
            entity.HasOne(e => e.Car)
                  .WithMany(c => c.CarDocuments)
                  .HasForeignKey(e => e.CarId)
                  .OnDelete(DeleteBehavior.Cascade);
        });

        // Car Images
        modelBuilder.Entity<CarImage>(entity =>
        {
            entity.HasIndex(e => new { e.CarId, e.IsPrimary });
            entity.HasOne(e => e.Car)
                  .WithMany(c => c.CarImages)
                  .HasForeignKey(e => e.CarId)
                  .OnDelete(DeleteBehavior.Cascade);
        });

        // Customer Documents
        modelBuilder.Entity<CustomerDocument>(entity =>
        {
            entity.HasOne(e => e.Customer)
                  .WithMany(c => c.CustomerDocuments)
                  .HasForeignKey(e => e.CustomerId)
                  .OnDelete(DeleteBehavior.Cascade);
        });

        // Supplier Documents
        modelBuilder.Entity<SupplierDocument>(entity =>
        {
            entity.HasOne(e => e.Supplier)
                  .WithMany(s => s.SupplierDocuments)
                  .HasForeignKey(e => e.SupplierId)
                  .OnDelete(DeleteBehavior.Cascade);
        });

        // Sale Documents
        modelBuilder.Entity<SaleDocument>(entity =>
        {
            entity.HasOne(e => e.Sale)
                  .WithMany(s => s.SaleDocuments)
                  .HasForeignKey(e => e.SaleId)
                  .OnDelete(DeleteBehavior.Cascade);
        });
    }

    private void ConfigureSupportEntities(ModelBuilder modelBuilder)
    {
        // User Permissions
        modelBuilder.Entity<UserPermission>(entity =>
        {
            entity.HasIndex(e => new { e.UserId, e.PermissionName }).IsUnique();
            entity.HasOne(e => e.User)
                  .WithMany(u => u.UserPermissions)
                  .HasForeignKey(e => e.UserId)
                  .OnDelete(DeleteBehavior.Cascade);
        });

        // System Settings
        modelBuilder.Entity<SystemSetting>(entity =>
        {
            entity.HasIndex(e => e.SettingKey).IsUnique();
            entity.Property(e => e.SettingKey).IsRequired();
        });

        // Authorized Installations
        modelBuilder.Entity<AuthorizedInstallation>(entity =>
        {
            entity.HasIndex(e => e.MachineId).IsUnique();
            entity.Property(e => e.MachineId).IsRequired();
        });

        // Supplier Payments
        modelBuilder.Entity<SupplierPayment>(entity =>
        {
            entity.Property(e => e.Amount).HasPrecision(18, 2);
            entity.HasOne(e => e.Supplier)
                  .WithMany(s => s.SupplierPayments)
                  .HasForeignKey(e => e.SupplierId)
                  .OnDelete(DeleteBehavior.Cascade);
        });
    }

    private void ConfigureIndexes(ModelBuilder modelBuilder)
    {
        // Additional performance indexes
        modelBuilder.Entity<Car>().HasIndex(e => e.Status);
        modelBuilder.Entity<Customer>().HasIndex(e => e.Status);
        modelBuilder.Entity<Supplier>().HasIndex(e => e.Status);
        modelBuilder.Entity<Sale>().HasIndex(e => e.Status);
        modelBuilder.Entity<InstallmentPayment>().HasIndex(e => e.Status);
        modelBuilder.Entity<User>().HasIndex(e => e.IsActive);
        modelBuilder.Entity<AuditLog>().HasIndex(e => e.ActionDate);
        modelBuilder.Entity<UserLoginLog>().HasIndex(e => e.LoginDate);
    }

    private void ConfigureEnumConversions(ModelBuilder modelBuilder)
    {
        // Configure enum to string conversions for better database compatibility
        modelBuilder.Entity<Car>().Property(e => e.Status).HasConversion<string>();
        modelBuilder.Entity<Customer>().Property(e => e.Status).HasConversion<string>();
        modelBuilder.Entity<Supplier>().Property(e => e.Status).HasConversion<string>();
        modelBuilder.Entity<Sale>().Property(e => e.Status).HasConversion<string>();
        modelBuilder.Entity<Sale>().Property(e => e.PaymentType).HasConversion<string>();
        modelBuilder.Entity<User>().Property(e => e.Role).HasConversion<string>();
        modelBuilder.Entity<InstallmentPayment>().Property(e => e.Status).HasConversion<string>();
        modelBuilder.Entity<InstallmentPayment>().Property(e => e.PaymentType).HasConversion<string>();
        modelBuilder.Entity<AuditLog>().Property(e => e.Action).HasConversion<string>();
        modelBuilder.Entity<AuthorizedInstallation>().Property(e => e.LicenseType).HasConversion<string>();
        modelBuilder.Entity<AuthorizedInstallation>().Property(e => e.Status).HasConversion<string>();
        modelBuilder.Entity<SupplierPayment>().Property(e => e.PaymentType).HasConversion<string>();
        
        // Document types
        modelBuilder.Entity<CarDocument>().Property(e => e.DocumentType).HasConversion<string>();
        modelBuilder.Entity<CustomerDocument>().Property(e => e.DocumentType).HasConversion<string>();
        modelBuilder.Entity<SupplierDocument>().Property(e => e.DocumentType).HasConversion<string>();
        modelBuilder.Entity<SaleDocument>().Property(e => e.DocumentType).HasConversion<string>();
    }

    private void ConfigureGlobalFilters(ModelBuilder modelBuilder)
    {
        // Global query filter for soft delete
        modelBuilder.Entity<Car>().HasQueryFilter(e => !e.IsDeleted);
        modelBuilder.Entity<Customer>().HasQueryFilter(e => !e.IsDeleted);
        modelBuilder.Entity<Supplier>().HasQueryFilter(e => !e.IsDeleted);
        modelBuilder.Entity<Sale>().HasQueryFilter(e => !e.IsDeleted);
        modelBuilder.Entity<User>().HasQueryFilter(e => !e.IsDeleted);
        modelBuilder.Entity<InstallmentPayment>().HasQueryFilter(e => !e.IsDeleted);
    }

    private void SeedData(ModelBuilder modelBuilder)
    {
        // Seed default system settings
        modelBuilder.Entity<SystemSetting>().HasData(
            new SystemSetting { Id = 1, SettingKey = "CompanyName", SettingValue = "معرض الصفا لتجارة واستيراد السيارات", Description = "اسم الشركة", CreatedAt = DateTime.Now },
            new SystemSetting { Id = 2, SettingKey = "CompanyPhone", SettingValue = "01285626623", Description = "هاتف الشركة", CreatedAt = DateTime.Now },
            new SystemSetting { Id = 3, SettingKey = "CompanyEmail", SettingValue = "<EMAIL>", Description = "بريد الشركة الإلكتروني", CreatedAt = DateTime.Now },
            new SystemSetting { Id = 4, SettingKey = "DefaultCurrency", SettingValue = "SAR", Description = "العملة الافتراضية", CreatedAt = DateTime.Now },
            new SystemSetting { Id = 5, SettingKey = "TaxRate", SettingValue = "15", Description = "معدل الضريبة المضافة", DataType = "decimal", CreatedAt = DateTime.Now },
            new SystemSetting { Id = 6, SettingKey = "BackupRetentionDays", SettingValue = "30", Description = "عدد أيام الاحتفاظ بالنسخ الاحتياطية", DataType = "int", CreatedAt = DateTime.Now }
        );

        // Seed default admin user
        modelBuilder.Entity<User>().HasData(
            new User
            {
                Id = 1,
                Username = "admin",
                PasswordHash = BCrypt.Net.BCrypt.HashPassword("admin123"),
                FirstName = "مدير",
                LastName = "النظام",
                Email = "<EMAIL>",
                Role = UserRole.Admin,
                IsActive = true,
                CreatedAt = DateTime.Now,
                HireDate = DateTime.Now
            }
        );
    }

    public override int SaveChanges()
    {
        UpdateTimestamps();
        return base.SaveChanges();
    }

    public override async Task<int> SaveChangesAsync(CancellationToken cancellationToken = default)
    {
        UpdateTimestamps();
        return await base.SaveChangesAsync(cancellationToken);
    }

    private void UpdateTimestamps()
    {
        var entries = ChangeTracker.Entries<BaseEntity>();

        foreach (var entry in entries)
        {
            switch (entry.State)
            {
                case EntityState.Added:
                    entry.Entity.CreatedAt = DateTime.Now;
                    break;
                case EntityState.Modified:
                    entry.Entity.UpdatedAt = DateTime.Now;
                    break;
            }
        }
    }
}
