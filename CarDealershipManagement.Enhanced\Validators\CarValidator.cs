using FluentValidation;
using CarDealershipManagement.Enhanced.Models;
using CarDealershipManagement.Enhanced.Services.Interfaces;

namespace CarDealershipManagement.Enhanced.Validators;

/// <summary>
/// فئة التحقق من صحة بيانات السيارة
/// </summary>
public class CarValidator : AbstractValidator<Car>
{
    private readonly ICarService _carService;

    public CarValidator(ICarService carService)
    {
        _carService = carService;

        RuleFor(c => c.ChassisNumber)
            .NotEmpty().WithMessage("رقم الشاسيه مطلوب")
            .Length(17).WithMessage("رقم الشاسيه يجب أن يكون 17 حرف")
            .Matches(@"^[A-HJ-NPR-Z0-9]{17}$").WithMessage("رقم الشاسيه غير صحيح")
            .MustAsync(BeUniqueChassisNumber).WithMessage("رقم الشاسيه موجود مسبقاً");

        RuleFor(c => c.Brand)
            .NotEmpty().WithMessage("الماركة مطلوبة")
            .Length(2, 50).WithMessage("الماركة يجب أن تكون بين 2 و 50 حرف");

        RuleFor(c => c.Model)
            .NotEmpty().WithMessage("الموديل مطلوب")
            .Length(2, 50).WithMessage("الموديل يجب أن يكون بين 2 و 50 حرف");

        RuleFor(c => c.Year)
            .InclusiveBetween(1900, DateTime.Now.Year + 1)
            .WithMessage($"سنة الصنع يجب أن تكون بين 1900 و {DateTime.Now.Year + 1}");

        RuleFor(c => c.Color)
            .NotEmpty().WithMessage("اللون مطلوب")
            .Length(2, 30).WithMessage("اللون يجب أن يكون بين 2 و 30 حرف");

        RuleFor(c => c.EngineNumber)
            .NotEmpty().WithMessage("رقم المحرك مطلوب")
            .Length(5, 30).WithMessage("رقم المحرك يجب أن يكون بين 5 و 30 حرف");

        RuleFor(c => c.PurchasePrice)
            .GreaterThan(0).WithMessage("سعر الشراء يجب أن يكون أكبر من صفر")
            .LessThan(10000000).WithMessage("سعر الشراء يجب أن يكون أقل من 10 مليون");

        RuleFor(c => c.SalePrice)
            .GreaterThan(0).WithMessage("سعر البيع يجب أن يكون أكبر من صفر")
            .LessThan(10000000).WithMessage("سعر البيع يجب أن يكون أقل من 10 مليون")
            .GreaterThanOrEqualTo(c => c.PurchasePrice).WithMessage("سعر البيع يجب أن يكون أكبر من أو يساوي سعر الشراء");

        RuleFor(c => c.PurchaseDate)
            .LessThanOrEqualTo(DateTime.Now).WithMessage("تاريخ الشراء لا يمكن أن يكون في المستقبل");

        RuleFor(c => c.SupplierId)
            .GreaterThan(0).WithMessage("المورد مطلوب");

        RuleFor(c => c.Mileage)
            .GreaterThanOrEqualTo(0).WithMessage("عدد الكيلومترات لا يمكن أن يكون سالب")
            .LessThan(1000000).WithMessage("عدد الكيلومترات يجب أن يكون أقل من مليون");

        RuleFor(c => c.FuelType)
            .NotEmpty().WithMessage("نوع الوقود مطلوب");

        RuleFor(c => c.Transmission)
            .NotEmpty().WithMessage("نوع ناقل الحركة مطلوب");

        When(c => !string.IsNullOrEmpty(c.LicensePlate), () =>
        {
            RuleFor(c => c.LicensePlate)
                .Matches(@"^[A-Z0-9\u0600-\u06FF\s-]{3,15}$")
                .WithMessage("رقم اللوحة غير صحيح");
        });

        When(c => c.CustomerId.HasValue, () =>
        {
            RuleFor(c => c.CustomerId)
                .GreaterThan(0).WithMessage("معرف العميل غير صحيح");
        });
    }

    private async Task<bool> BeUniqueChassisNumber(Car car, string chassisNumber, CancellationToken cancellationToken)
    {
        return await _carService.IsChassisNumberAvailableAsync(chassisNumber, car.Id);
    }
}

/// <summary>
/// فئة التحقق من صحة بيانات العميل
/// </summary>
public class CustomerValidator : AbstractValidator<Customer>
{
    private readonly ICustomerService _customerService;

    public CustomerValidator(ICustomerService customerService)
    {
        _customerService = customerService;

        RuleFor(c => c.FirstName)
            .NotEmpty().WithMessage("الاسم الأول مطلوب")
            .Length(2, 50).WithMessage("الاسم الأول يجب أن يكون بين 2 و 50 حرف")
            .Matches(@"^[\u0600-\u06FFa-zA-Z\s]+$").WithMessage("الاسم الأول يجب أن يحتوي على أحرف فقط");

        RuleFor(c => c.LastName)
            .NotEmpty().WithMessage("الاسم الأخير مطلوب")
            .Length(2, 50).WithMessage("الاسم الأخير يجب أن يكون بين 2 و 50 حرف")
            .Matches(@"^[\u0600-\u06FFa-zA-Z\s]+$").WithMessage("الاسم الأخير يجب أن يحتوي على أحرف فقط");

        RuleFor(c => c.Phone)
            .NotEmpty().WithMessage("رقم الهاتف مطلوب")
            .Matches(@"^(\+?[0-9]{10,15})$").WithMessage("رقم الهاتف غير صحيح")
            .MustAsync(BeUniquePhone).WithMessage("رقم الهاتف موجود مسبقاً");

        RuleFor(c => c.NationalId)
            .NotEmpty().WithMessage("رقم الهوية مطلوب")
            .Length(10, 20).WithMessage("رقم الهوية يجب أن يكون بين 10 و 20 رقم")
            .Matches(@"^[0-9]+$").WithMessage("رقم الهوية يجب أن يحتوي على أرقام فقط")
            .MustAsync(BeUniqueNationalId).WithMessage("رقم الهوية موجود مسبقاً");

        When(c => !string.IsNullOrEmpty(c.Email), () =>
        {
            RuleFor(c => c.Email)
                .EmailAddress().WithMessage("البريد الإلكتروني غير صحيح")
                .MustAsync(BeUniqueEmail).WithMessage("البريد الإلكتروني موجود مسبقاً");
        });

        RuleFor(c => c.DateOfBirth)
            .LessThan(DateTime.Now.AddYears(-16)).WithMessage("العميل يجب أن يكون أكبر من 16 سنة")
            .GreaterThan(DateTime.Now.AddYears(-120)).WithMessage("تاريخ الميلاد غير منطقي");

        RuleFor(c => c.CreditLimit)
            .GreaterThanOrEqualTo(0).WithMessage("الحد الائتماني لا يمكن أن يكون سالب")
            .LessThan(10000000).WithMessage("الحد الائتماني يجب أن يكون أقل من 10 مليون");

        When(c => c.SalesAgentId.HasValue, () =>
        {
            RuleFor(c => c.SalesAgentId)
                .GreaterThan(0).WithMessage("معرف مندوب المبيعات غير صحيح");
        });
    }

    private async Task<bool> BeUniquePhone(Customer customer, string phone, CancellationToken cancellationToken)
    {
        return await _customerService.IsPhoneAvailableAsync(phone, customer.Id);
    }

    private async Task<bool> BeUniqueNationalId(Customer customer, string nationalId, CancellationToken cancellationToken)
    {
        return await _customerService.IsNationalIdAvailableAsync(nationalId, customer.Id);
    }

    private async Task<bool> BeUniqueEmail(Customer customer, string email, CancellationToken cancellationToken)
    {
        return await _customerService.IsEmailAvailableAsync(email, customer.Id);
    }
}

/// <summary>
/// فئة التحقق من صحة بيانات المورد
/// </summary>
public class SupplierValidator : AbstractValidator<Supplier>
{
    private readonly ISupplierService _supplierService;

    public SupplierValidator(ISupplierService supplierService)
    {
        _supplierService = supplierService;

        RuleFor(s => s.CompanyName)
            .NotEmpty().WithMessage("اسم الشركة مطلوب")
            .Length(2, 100).WithMessage("اسم الشركة يجب أن يكون بين 2 و 100 حرف");

        RuleFor(s => s.ContactPersonName)
            .NotEmpty().WithMessage("اسم الشخص المسؤول مطلوب")
            .Length(2, 100).WithMessage("اسم الشخص المسؤول يجب أن يكون بين 2 و 100 حرف")
            .Matches(@"^[\u0600-\u06FFa-zA-Z\s]+$").WithMessage("اسم الشخص المسؤول يجب أن يحتوي على أحرف فقط");

        RuleFor(s => s.Phone)
            .NotEmpty().WithMessage("رقم الهاتف مطلوب")
            .Matches(@"^(\+?[0-9]{10,15})$").WithMessage("رقم الهاتف غير صحيح")
            .MustAsync(BeUniquePhone).WithMessage("رقم الهاتف موجود مسبقاً");

        RuleFor(s => s.RegistrationNumber)
            .NotEmpty().WithMessage("رقم التسجيل التجاري مطلوب")
            .Length(5, 30).WithMessage("رقم التسجيل التجاري يجب أن يكون بين 5 و 30 حرف")
            .MustAsync(BeUniqueRegistrationNumber).WithMessage("رقم التسجيل التجاري موجود مسبقاً");

        When(s => !string.IsNullOrEmpty(s.Email), () =>
        {
            RuleFor(s => s.Email)
                .EmailAddress().WithMessage("البريد الإلكتروني غير صحيح")
                .MustAsync(BeUniqueEmail).WithMessage("البريد الإلكتروني موجود مسبقاً");
        });

        RuleFor(s => s.Rating)
            .InclusiveBetween(0, 5).WithMessage("التقييم يجب أن يكون بين 0 و 5");

        RuleFor(s => s.PaymentTerms)
            .GreaterThanOrEqualTo(0).WithMessage("شروط الدفع لا يمكن أن تكون سالبة")
            .LessThanOrEqualTo(365).WithMessage("شروط الدفع يجب أن تكون أقل من أو تساوي 365 يوم");
    }

    private async Task<bool> BeUniquePhone(Supplier supplier, string phone, CancellationToken cancellationToken)
    {
        return await _supplierService.IsPhoneAvailableAsync(phone, supplier.Id);
    }

    private async Task<bool> BeUniqueRegistrationNumber(Supplier supplier, string registrationNumber, CancellationToken cancellationToken)
    {
        return await _supplierService.IsRegistrationNumberAvailableAsync(registrationNumber, supplier.Id);
    }

    private async Task<bool> BeUniqueEmail(Supplier supplier, string email, CancellationToken cancellationToken)
    {
        return await _supplierService.IsEmailAvailableAsync(email, supplier.Id);
    }
}
