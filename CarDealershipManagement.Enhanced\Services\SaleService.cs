using Microsoft.EntityFrameworkCore;
using CarDealershipManagement.Enhanced.Data;
using CarDealershipManagement.Enhanced.Models;
using CarDealershipManagement.Enhanced.Enums;
using CarDealershipManagement.Enhanced.Services.Interfaces;

namespace CarDealershipManagement.Enhanced.Services;

/// <summary>
/// خدمة المبيعات
/// </summary>
public class SaleService : BaseService<Sale>, ISaleService
{
    public SaleService(CarDealershipDbContext context) : base(context)
    {
    }

    protected override IQueryable<Sale> ApplyIncludes(IQueryable<Sale> query)
    {
        return query
            .Include(s => s.Car)
            .Include(s => s.Customer)
            .Include(s => s.SalesAgent)
            .Include(s => s.InstallmentPayments)
            .Include(s => s.SaleDocuments);
    }

    public async Task<Sale?> GetBySaleNumberAsync(string saleNumber)
    {
        return await ApplyIncludes(_dbSet)
            .FirstOrDefaultAsync(s => s.SaleNumber == saleNumber);
    }

    public async Task<IEnumerable<Sale>> GetByStatusAsync(SaleStatus status)
    {
        return await ApplyIncludes(_dbSet)
            .Where(s => s.Status == status)
            .ToListAsync();
    }

    public async Task<IEnumerable<Sale>> GetByCustomerAsync(int customerId)
    {
        return await ApplyIncludes(_dbSet)
            .Where(s => s.CustomerId == customerId)
            .ToListAsync();
    }

    public async Task<IEnumerable<Sale>> GetBySalesAgentAsync(int salesAgentId)
    {
        return await ApplyIncludes(_dbSet)
            .Where(s => s.SalesAgentId == salesAgentId)
            .ToListAsync();
    }

    public async Task<IEnumerable<Sale>> GetByDateRangeAsync(DateTime startDate, DateTime endDate)
    {
        return await ApplyIncludes(_dbSet)
            .Where(s => s.SaleDate >= startDate && s.SaleDate <= endDate)
            .ToListAsync();
    }

    public async Task<Sale> CreateSaleAsync(
        int carId,
        int customerId,
        decimal salePrice,
        PaymentType paymentType,
        int salesAgentId,
        int? userId = null)
    {
        // التحقق من إمكانية بيع السيارة
        if (!await CanSellCarAsync(carId))
            throw new InvalidOperationException("لا يمكن بيع هذه السيارة");

        // التحقق من الحد الائتماني للعميل
        if (!await CheckCustomerCreditLimitAsync(customerId, salePrice))
            throw new InvalidOperationException("تجاوز العميل الحد الائتماني المسموح");

        var car = await _context.Cars.FindAsync(carId);
        var customer = await _context.Customers.FindAsync(customerId);

        if (car == null || customer == null)
            throw new ArgumentException("السيارة أو العميل غير موجود");

        var saleNumber = await GenerateSaleNumberAsync();

        var sale = new Sale
        {
            SaleNumber = saleNumber,
            CarId = carId,
            CustomerId = customerId,
            SalesAgentId = salesAgentId,
            SaleDate = DateTime.Now,
            SalePrice = salePrice,
            TotalAmount = salePrice, // يمكن إضافة ضرائب ورسوم لاحقاً
            PaymentType = paymentType,
            Status = SaleStatus.Pending,
            CreatedAt = DateTime.Now,
            CreatedBy = userId
        };

        // حساب الربح
        sale.ProfitAmount = sale.SalePrice - car.PurchasePrice;

        await AddAsync(sale, userId);

        // تحديث حالة السيارة
        car.Status = CarStatus.Reserved;
        car.CustomerId = customerId;
        car.UpdateModificationInfo(userId);

        await _context.SaveChangesAsync();

        return sale;
    }

    public async Task<bool> ConfirmSaleAsync(int saleId, int? userId = null)
    {
        var sale = await GetByIdAsync(saleId);
        if (sale == null || sale.Status != SaleStatus.Pending)
            return false;

        sale.Status = SaleStatus.Confirmed;
        sale.ConfirmationDate = DateTime.Now;
        sale.UpdateModificationInfo(userId);

        // تحديث حالة السيارة
        var car = await _context.Cars.FindAsync(sale.CarId);
        if (car != null)
        {
            car.Status = CarStatus.Sold;
            car.UpdateModificationInfo(userId);
        }

        // تحديث بيانات العميل
        var customer = await _context.Customers.FindAsync(sale.CustomerId);
        if (customer != null)
        {
            customer.LastPurchaseDate = DateTime.Now;
            customer.TotalPurchases += sale.TotalAmount;
            customer.UpdateModificationInfo(userId);
        }

        await UpdateAsync(sale, userId);
        return true;
    }

    public async Task<bool> CancelSaleAsync(int saleId, string reason, int? userId = null)
    {
        var sale = await GetByIdAsync(saleId);
        if (sale == null)
            return false;

        sale.Status = SaleStatus.Cancelled;
        sale.CancellationReason = reason;
        sale.UpdateModificationInfo(userId);

        // إعادة تعيين حالة السيارة
        var car = await _context.Cars.FindAsync(sale.CarId);
        if (car != null)
        {
            car.Status = CarStatus.Available;
            car.CustomerId = null;
            car.UpdateModificationInfo(userId);
        }

        await UpdateAsync(sale, userId);
        return true;
    }

    public async Task<bool> UpdateSaleStatusAsync(int saleId, SaleStatus status, int? userId = null)
    {
        var sale = await GetByIdAsync(saleId);
        if (sale == null)
            return false;

        sale.Status = status;
        sale.UpdateModificationInfo(userId);

        await UpdateAsync(sale, userId);
        return true;
    }

    public async Task<bool> AddPaymentAsync(int saleId, decimal amount, PaymentType paymentType, int? userId = null)
    {
        var sale = await GetByIdAsync(saleId);
        if (sale == null)
            return false;

        sale.PaidAmount += amount;
        sale.UpdateModificationInfo(userId);

        // إذا تم دفع المبلغ كاملاً، تحديث الحالة
        if (sale.PaidAmount >= sale.TotalAmount)
        {
            sale.Status = SaleStatus.Completed;
        }

        await UpdateAsync(sale, userId);
        return true;
    }

    public async Task<IEnumerable<InstallmentPayment>> CreateInstallmentScheduleAsync(
        int saleId,
        int numberOfInstallments,
        DateTime firstInstallmentDate,
        int? userId = null)
    {
        var sale = await GetByIdAsync(saleId);
        if (sale == null)
            throw new ArgumentException("البيع غير موجود");

        var installments = new List<InstallmentPayment>();
        var installmentAmount = (sale.TotalAmount - sale.DownPayment) / numberOfInstallments;

        for (int i = 1; i <= numberOfInstallments; i++)
        {
            var installment = new InstallmentPayment
            {
                SaleId = saleId,
                InstallmentNumber = i,
                Amount = installmentAmount,
                DueDate = firstInstallmentDate.AddMonths(i - 1),
                Status = InstallmentStatus.Pending,
                CreatedAt = DateTime.Now,
                CreatedBy = userId
            };

            installments.Add(installment);
        }

        _context.InstallmentPayments.AddRange(installments);
        await _context.SaveChangesAsync();

        return installments;
    }

    public async Task<IEnumerable<InstallmentPayment>> GetSaleInstallmentsAsync(int saleId)
    {
        return await _context.InstallmentPayments
            .Where(i => i.SaleId == saleId)
            .OrderBy(i => i.InstallmentNumber)
            .ToListAsync();
    }

    public async Task<IEnumerable<InstallmentPayment>> GetOverdueInstallmentsAsync(int saleId)
    {
        return await _context.InstallmentPayments
            .Where(i => i.SaleId == saleId && i.Status == InstallmentStatus.Overdue)
            .OrderBy(i => i.DueDate)
            .ToListAsync();
    }

    public async Task<decimal> CalculateSaleProfitAsync(int saleId)
    {
        var sale = await GetByIdAsync(saleId);
        return sale?.ProfitAmount ?? 0;
    }

    public async Task<decimal> CalculateSalesCommissionAsync(int saleId)
    {
        var sale = await GetByIdAsync(saleId);
        if (sale == null) return 0;

        // حساب العمولة كنسبة من الربح (يمكن تخصيصها)
        var commissionRate = 0.05m; // 5%
        return sale.ProfitAmount * commissionRate;
    }

    public async Task<SaleDocument> AddSaleDocumentAsync(int saleId, SaleDocument document, int? userId = null)
    {
        document.SaleId = saleId;
        document.CreatedAt = DateTime.Now;
        document.CreatedBy = userId;

        _context.SaleDocuments.Add(document);
        await _context.SaveChangesAsync();

        return document;
    }

    public async Task<IEnumerable<SaleDocument>> GetSaleDocumentsAsync(int saleId)
    {
        return await _context.SaleDocuments
            .Where(d => d.SaleId == saleId)
            .OrderBy(d => d.DocumentType)
            .ThenBy(d => d.CreatedAt)
            .ToListAsync();
    }

    public async Task<SalesStatistics> GetSalesStatisticsAsync(DateTime? startDate = null, DateTime? endDate = null)
    {
        var query = _dbSet.AsQueryable();

        if (startDate.HasValue)
            query = query.Where(s => s.SaleDate >= startDate.Value);

        if (endDate.HasValue)
            query = query.Where(s => s.SaleDate <= endDate.Value);

        var totalSales = await query.CountAsync();
        var totalSalesValue = await query.SumAsync(s => s.TotalAmount);
        var totalProfit = await query.SumAsync(s => s.ProfitAmount);
        var averageSaleValue = totalSales > 0 ? totalSalesValue / totalSales : 0;
        var highestSaleValue = totalSales > 0 ? await query.MaxAsync(s => s.TotalAmount) : 0;

        var cashSales = await query.CountAsync(s => s.PaymentType == PaymentType.Cash);
        var installmentSales = await query.CountAsync(s => s.PaymentType == PaymentType.Installment);
        var confirmedSales = await query.CountAsync(s => s.Status == SaleStatus.Confirmed);
        var pendingSales = await query.CountAsync(s => s.Status == SaleStatus.Pending);
        var cancelledSales = await query.CountAsync(s => s.Status == SaleStatus.Cancelled);

        var totalCommissions = totalProfit * 0.05m; // 5% عمولة

        return new SalesStatistics
        {
            TotalSales = totalSales,
            TotalSalesValue = totalSalesValue,
            TotalProfit = totalProfit,
            AverageSaleValue = averageSaleValue,
            HighestSaleValue = highestSaleValue,
            CashSales = cashSales,
            InstallmentSales = installmentSales,
            ConfirmedSales = confirmedSales,
            PendingSales = pendingSales,
            CancelledSales = cancelledSales,
            TotalCommissions = totalCommissions
        };
    }

    public async Task<IEnumerable<Sale>> GetPendingSalesAsync()
    {
        return await GetByStatusAsync(SaleStatus.Pending);
    }

    public async Task<IEnumerable<SalesAgentPerformance>> GetTopSalesAgentsAsync(
        DateTime? startDate = null,
        DateTime? endDate = null,
        int count = 10)
    {
        var query = _dbSet.AsQueryable();

        if (startDate.HasValue)
            query = query.Where(s => s.SaleDate >= startDate.Value);

        if (endDate.HasValue)
            query = query.Where(s => s.SaleDate <= endDate.Value);

        return await query
            .Where(s => s.Status == SaleStatus.Confirmed)
            .GroupBy(s => new { s.SalesAgentId, s.SalesAgent.FirstName, s.SalesAgent.LastName })
            .Select(g => new SalesAgentPerformance
            {
                SalesAgentId = g.Key.SalesAgentId,
                SalesAgentName = $"{g.Key.FirstName} {g.Key.LastName}",
                SalesCount = g.Count(),
                TotalSalesValue = g.Sum(s => s.TotalAmount),
                TotalCommissions = g.Sum(s => s.ProfitAmount) * 0.05m,
                AverageSaleValue = g.Average(s => s.TotalAmount)
            })
            .OrderByDescending(p => p.TotalSalesValue)
            .Take(count)
            .ToListAsync();
    }

    public async Task<string> GenerateSaleNumberAsync()
    {
        var year = DateTime.Now.Year;
        var month = DateTime.Now.Month;
        var prefix = $"S{year:D4}{month:D2}";

        var lastSale = await _dbSet
            .Where(s => s.SaleNumber.StartsWith(prefix))
            .OrderByDescending(s => s.SaleNumber)
            .FirstOrDefaultAsync();

        int nextNumber = 1;
        if (lastSale != null && lastSale.SaleNumber.Length > prefix.Length)
        {
            var numberPart = lastSale.SaleNumber.Substring(prefix.Length);
            if (int.TryParse(numberPart, out int lastNumber))
            {
                nextNumber = lastNumber + 1;
            }
        }

        return $"{prefix}{nextNumber:D4}";
    }

    public async Task<bool> CanSellCarAsync(int carId)
    {
        var car = await _context.Cars.FindAsync(carId);
        return car != null && car.Status == CarStatus.Available;
    }

    public async Task<bool> CheckCustomerCreditLimitAsync(int customerId, decimal saleAmount)
    {
        var customer = await _context.Customers.FindAsync(customerId);
        if (customer == null) return false;

        var currentBalance = await _context.InstallmentPayments
            .Where(i => i.Sale.CustomerId == customerId && i.Status != InstallmentStatus.Paid)
            .SumAsync(i => i.Amount - i.PaidAmount);

        return (currentBalance + saleAmount) <= customer.CreditLimit;
    }

    protected override async Task<bool> ValidateEntityAsync(Sale entity)
    {
        // التحقق من وجود السيارة والعميل ومندوب المبيعات
        var car = await _context.Cars.FindAsync(entity.CarId);
        var customer = await _context.Customers.FindAsync(entity.CustomerId);
        var salesAgent = await _context.Users.FindAsync(entity.SalesAgentId);

        if (car == null || customer == null || salesAgent == null)
            return false;

        // التحقق من صحة البيانات المالية
        if (entity.SalePrice <= 0 || entity.TotalAmount <= 0)
            return false;

        return true;
    }

    protected override async Task OnAfterAddAsync(Sale entity, int? userId = null)
    {
        // تسجيل العملية في سجل التدقيق
        var auditLog = new AuditLog
        {
            TableName = "Sales",
            RecordId = entity.Id,
            Action = AuditAction.Insert,
            OldValues = null,
            NewValues = $"بيع جديد رقم: {entity.SaleNumber}",
            UserId = userId,
            Timestamp = DateTime.Now
        };

        _context.AuditLogs.Add(auditLog);
        await _context.SaveChangesAsync();

        await base.OnAfterAddAsync(entity, userId);
    }
}
