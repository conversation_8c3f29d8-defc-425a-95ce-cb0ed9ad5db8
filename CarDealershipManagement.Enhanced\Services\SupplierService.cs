using Microsoft.EntityFrameworkCore;
using CarDealershipManagement.Enhanced.Data;
using CarDealershipManagement.Enhanced.Models;
using CarDealershipManagement.Enhanced.Enums;
using CarDealershipManagement.Enhanced.Services.Interfaces;

namespace CarDealershipManagement.Enhanced.Services;

/// <summary>
/// خدمة الموردين
/// </summary>
public class SupplierService : BaseService<Supplier>, ISupplierService
{
    public SupplierService(CarDealershipDbContext context) : base(context)
    {
    }

    protected override IQueryable<Supplier> ApplyIncludes(IQueryable<Supplier> query)
    {
        return query
            .Include(s => s.Cars)
            .Include(s => s.SupplierDocuments)
            .Include(s => s.SupplierPayments)
            .Include(s => s.SupplierContracts);
    }

    public async Task<Supplier?> GetByRegistrationNumberAsync(string registrationNumber)
    {
        return await ApplyIncludes(_dbSet)
            .FirstOrDefaultAsync(s => s.RegistrationNumber == registrationNumber);
    }

    public async Task<Supplier?> GetByPhoneAsync(string phone)
    {
        return await ApplyIncludes(_dbSet)
            .FirstOrDefaultAsync(s => s.Phone == phone);
    }

    public async Task<Supplier?> GetByEmailAsync(string email)
    {
        return await ApplyIncludes(_dbSet)
            .FirstOrDefaultAsync(s => s.Email == email);
    }

    public async Task<IEnumerable<Supplier>> GetByStatusAsync(SupplierStatus status)
    {
        return await ApplyIncludes(_dbSet)
            .Where(s => s.Status == status)
            .ToListAsync();
    }

    public async Task<IEnumerable<Supplier>> GetActiveSuppliers()
    {
        return await GetByStatusAsync(SupplierStatus.Active);
    }

    public async Task<IEnumerable<Supplier>> SearchSuppliersAsync(
        string? searchTerm = null,
        SupplierStatus? status = null,
        string? city = null,
        string? country = null)
    {
        var query = ApplyIncludes(_dbSet);

        if (!string.IsNullOrWhiteSpace(searchTerm))
        {
            query = query.Where(s =>
                s.CompanyName.Contains(searchTerm) ||
                s.ContactPersonName.Contains(searchTerm) ||
                s.Phone.Contains(searchTerm) ||
                s.Email.Contains(searchTerm) ||
                s.RegistrationNumber.Contains(searchTerm));
        }

        if (status.HasValue)
            query = query.Where(s => s.Status == status.Value);

        if (!string.IsNullOrWhiteSpace(city))
            query = query.Where(s => s.City.Contains(city));

        if (!string.IsNullOrWhiteSpace(country))
            query = query.Where(s => s.Country.Contains(country));

        return await query.ToListAsync();
    }

    public async Task<bool> UpdateSupplierStatusAsync(int supplierId, SupplierStatus status, int? userId = null)
    {
        var supplier = await GetByIdAsync(supplierId);
        if (supplier == null)
            return false;

        supplier.Status = status;
        supplier.UpdateModificationInfo(userId);

        await UpdateAsync(supplier, userId);
        return true;
    }

    public async Task<bool> UpdateSupplierRatingAsync(int supplierId, int rating, int? userId = null)
    {
        if (rating < 1 || rating > 5)
            return false;

        var supplier = await GetByIdAsync(supplierId);
        if (supplier == null)
            return false;

        supplier.Rating = rating;
        supplier.UpdateModificationInfo(userId);

        await UpdateAsync(supplier, userId);
        return true;
    }

    public async Task<SupplierDocument> AddSupplierDocumentAsync(int supplierId, SupplierDocument document, int? userId = null)
    {
        document.SupplierId = supplierId;
        document.CreatedAt = DateTime.Now;
        document.CreatedBy = userId;

        _context.SupplierDocuments.Add(document);
        await _context.SaveChangesAsync();

        return document;
    }

    public async Task<IEnumerable<SupplierDocument>> GetSupplierDocumentsAsync(int supplierId)
    {
        return await _context.SupplierDocuments
            .Where(d => d.SupplierId == supplierId)
            .OrderBy(d => d.DocumentType)
            .ThenBy(d => d.CreatedAt)
            .ToListAsync();
    }

    public async Task<bool> DeleteSupplierDocumentAsync(int documentId, int? userId = null)
    {
        var document = await _context.SupplierDocuments.FindAsync(documentId);
        if (document == null)
            return false;

        document.SoftDelete(userId);
        await _context.SaveChangesAsync();

        return true;
    }

    public async Task<SupplierPayment> AddSupplierPaymentAsync(int supplierId, SupplierPayment payment, int? userId = null)
    {
        payment.SupplierId = supplierId;
        payment.CreatedAt = DateTime.Now;
        payment.CreatedBy = userId;

        _context.SupplierPayments.Add(payment);
        await _context.SaveChangesAsync();

        return payment;
    }

    public async Task<IEnumerable<SupplierPayment>> GetSupplierPaymentsAsync(int supplierId)
    {
        return await _context.SupplierPayments
            .Where(p => p.SupplierId == supplierId)
            .OrderByDescending(p => p.PaymentDate)
            .ToListAsync();
    }

    public async Task<SupplierContract> AddSupplierContractAsync(int supplierId, SupplierContract contract, int? userId = null)
    {
        contract.SupplierId = supplierId;
        contract.CreatedAt = DateTime.Now;
        contract.CreatedBy = userId;

        _context.SupplierContracts.Add(contract);
        await _context.SaveChangesAsync();

        return contract;
    }

    public async Task<IEnumerable<SupplierContract>> GetSupplierContractsAsync(int supplierId)
    {
        return await _context.SupplierContracts
            .Where(c => c.SupplierId == supplierId)
            .OrderByDescending(c => c.StartDate)
            .ToListAsync();
    }

    public async Task<IEnumerable<Car>> GetSupplierCarsAsync(int supplierId)
    {
        return await _context.Cars
            .Where(c => c.SupplierId == supplierId)
            .OrderByDescending(c => c.PurchaseDate)
            .ToListAsync();
    }

    public async Task<decimal> CalculateSupplierTotalPurchasesAsync(int supplierId)
    {
        return await _context.Cars
            .Where(c => c.SupplierId == supplierId)
            .SumAsync(c => c.PurchasePrice);
    }

    public async Task<decimal> CalculateSupplierOutstandingBalanceAsync(int supplierId)
    {
        var totalPurchases = await CalculateSupplierTotalPurchasesAsync(supplierId);
        var totalPayments = await _context.SupplierPayments
            .Where(p => p.SupplierId == supplierId)
            .SumAsync(p => p.Amount);

        return totalPurchases - totalPayments;
    }

    public async Task<SupplierStatistics> GetSupplierStatisticsAsync()
    {
        var totalSuppliers = await _dbSet.CountAsync();
        var activeSuppliers = await _dbSet.CountAsync(s => s.Status == SupplierStatus.Active);
        var newSuppliersThisMonth = await _dbSet.CountAsync(s => s.CreatedAt.Month == DateTime.Now.Month && s.CreatedAt.Year == DateTime.Now.Year);
        var suppliersWithOutstandingBalance = await _dbSet.CountAsync(s => s.TotalPurchases > s.TotalPayments);

        var totalPurchasesFromSuppliers = await _context.Cars.SumAsync(c => c.PurchasePrice);
        var averagePurchasePerSupplier = totalSuppliers > 0 ? totalPurchasesFromSuppliers / totalSuppliers : 0;
        var highestSupplierPurchase = totalSuppliers > 0 ? await _dbSet.MaxAsync(s => s.TotalPurchases) : 0;

        var totalOutstandingToSuppliers = await _dbSet.SumAsync(s => s.TotalPurchases - s.TotalPayments);
        var averageRating = await _dbSet.Where(s => s.Rating > 0).AverageAsync(s => (double)s.Rating);

        var carsPurchasedThisMonth = await _context.Cars
            .CountAsync(c => c.PurchaseDate.Month == DateTime.Now.Month && c.PurchaseDate.Year == DateTime.Now.Year);

        return new SupplierStatistics
        {
            TotalSuppliers = totalSuppliers,
            ActiveSuppliers = activeSuppliers,
            NewSuppliersThisMonth = newSuppliersThisMonth,
            SuppliersWithOutstandingBalance = suppliersWithOutstandingBalance,
            TotalPurchasesFromSuppliers = totalPurchasesFromSuppliers,
            AveragePurchasePerSupplier = averagePurchasePerSupplier,
            HighestSupplierPurchase = highestSupplierPurchase,
            TotalOutstandingToSuppliers = totalOutstandingToSuppliers,
            AverageRating = averageRating,
            CarsPurchasedThisMonth = carsPurchasedThisMonth
        };
    }

    public async Task<IEnumerable<Supplier>> GetTopSuppliersAsync(int count = 10)
    {
        return await ApplyIncludes(_dbSet)
            .OrderByDescending(s => s.TotalPurchases)
            .Take(count)
            .ToListAsync();
    }

    public async Task<IEnumerable<Supplier>> GetSuppliersWithOutstandingBalanceAsync()
    {
        return await ApplyIncludes(_dbSet)
            .Where(s => s.TotalPurchases > s.TotalPayments)
            .OrderByDescending(s => s.TotalPurchases - s.TotalPayments)
            .ToListAsync();
    }

    public async Task<bool> IsRegistrationNumberAvailableAsync(string registrationNumber, int? excludeSupplierId = null)
    {
        var query = _dbSet.Where(s => s.RegistrationNumber == registrationNumber);

        if (excludeSupplierId.HasValue)
            query = query.Where(s => s.Id != excludeSupplierId.Value);

        return !await query.AnyAsync();
    }

    public async Task<bool> IsPhoneAvailableAsync(string phone, int? excludeSupplierId = null)
    {
        var query = _dbSet.Where(s => s.Phone == phone);

        if (excludeSupplierId.HasValue)
            query = query.Where(s => s.Id != excludeSupplierId.Value);

        return !await query.AnyAsync();
    }

    public async Task<bool> IsEmailAvailableAsync(string email, int? excludeSupplierId = null)
    {
        var query = _dbSet.Where(s => s.Email == email);

        if (excludeSupplierId.HasValue)
            query = query.Where(s => s.Id != excludeSupplierId.Value);

        return !await query.AnyAsync();
    }

    protected override async Task<bool> ValidateEntityAsync(Supplier entity)
    {
        // التحقق من عدم تكرار رقم التسجيل التجاري
        if (!await IsRegistrationNumberAvailableAsync(entity.RegistrationNumber, entity.Id))
            return false;

        // التحقق من عدم تكرار رقم الهاتف
        if (!await IsPhoneAvailableAsync(entity.Phone, entity.Id))
            return false;

        // التحقق من عدم تكرار البريد الإلكتروني
        if (!string.IsNullOrWhiteSpace(entity.Email) && !await IsEmailAvailableAsync(entity.Email, entity.Id))
            return false;

        // التحقق من صحة التقييم
        if (entity.Rating < 0 || entity.Rating > 5)
            return false;

        return true;
    }

    protected override async Task OnAfterAddAsync(Supplier entity, int? userId = null)
    {
        // تسجيل العملية في سجل التدقيق
        var auditLog = new AuditLog
        {
            TableName = "Suppliers",
            RecordId = entity.Id,
            Action = AuditAction.Insert,
            OldValues = null,
            NewValues = $"مورد جديد: {entity.CompanyName}",
            UserId = userId,
            Timestamp = DateTime.Now
        };

        _context.AuditLogs.Add(auditLog);
        await _context.SaveChangesAsync();

        await base.OnAfterAddAsync(entity, userId);
    }

    protected override async Task OnAfterUpdateAsync(Supplier entity, int? userId = null)
    {
        // تحديث إجمالي المشتريات والدفعات
        entity.TotalPurchases = await CalculateSupplierTotalPurchasesAsync(entity.Id);
        entity.TotalPayments = await _context.SupplierPayments
            .Where(p => p.SupplierId == entity.Id)
            .SumAsync(p => p.Amount);

        await base.OnAfterUpdateAsync(entity, userId);
    }
}
