using Microsoft.EntityFrameworkCore;
using CarDealershipManagement.Enhanced.Data;
using CarDealershipManagement.Enhanced.Models;
using CarDealershipManagement.Enhanced.Enums;
using CarDealershipManagement.Enhanced.Services.Interfaces;

namespace CarDealershipManagement.Enhanced.Services;

/// <summary>
/// خدمة الأقساط
/// </summary>
public class InstallmentService : BaseService<InstallmentPayment>, IInstallmentService
{
    public InstallmentService(CarDealershipDbContext context) : base(context)
    {
    }

    protected override IQueryable<InstallmentPayment> ApplyIncludes(IQueryable<InstallmentPayment> query)
    {
        return query
            .Include(i => i.Sale)
                .ThenInclude(s => s.Customer)
            .Include(i => i.Sale)
                .ThenInclude(s => s.Car);
    }

    public async Task<IEnumerable<InstallmentPayment>> GetBySaleAsync(int saleId)
    {
        return await ApplyIncludes(_dbSet)
            .Where(i => i.SaleId == saleId)
            .OrderBy(i => i.InstallmentNumber)
            .ToListAsync();
    }

    public async Task<IEnumerable<InstallmentPayment>> GetByCustomerAsync(int customerId)
    {
        return await ApplyIncludes(_dbSet)
            .Where(i => i.Sale.CustomerId == customerId)
            .OrderBy(i => i.DueDate)
            .ToListAsync();
    }

    public async Task<IEnumerable<InstallmentPayment>> GetByStatusAsync(InstallmentStatus status)
    {
        return await ApplyIncludes(_dbSet)
            .Where(i => i.Status == status)
            .OrderBy(i => i.DueDate)
            .ToListAsync();
    }

    public async Task<IEnumerable<InstallmentPayment>> GetDueInstallmentsAsync(DateTime? dueDate = null)
    {
        var targetDate = dueDate ?? DateTime.Today;
        
        return await ApplyIncludes(_dbSet)
            .Where(i => i.DueDate <= targetDate && i.Status == InstallmentStatus.Pending)
            .OrderBy(i => i.DueDate)
            .ToListAsync();
    }

    public async Task<IEnumerable<InstallmentPayment>> GetOverdueInstallmentsAsync()
    {
        return await ApplyIncludes(_dbSet)
            .Where(i => i.DueDate < DateTime.Today && i.Status == InstallmentStatus.Pending)
            .OrderBy(i => i.DueDate)
            .ToListAsync();
    }

    public async Task<IEnumerable<InstallmentPayment>> GetPaidInstallmentsAsync(DateTime? startDate = null, DateTime? endDate = null)
    {
        var query = ApplyIncludes(_dbSet).Where(i => i.Status == InstallmentStatus.Paid);

        if (startDate.HasValue)
            query = query.Where(i => i.PaymentDate >= startDate.Value);

        if (endDate.HasValue)
            query = query.Where(i => i.PaymentDate <= endDate.Value);

        return await query
            .OrderByDescending(i => i.PaymentDate)
            .ToListAsync();
    }

    public async Task<IEnumerable<InstallmentPayment>> CreateInstallmentScheduleAsync(
        int saleId,
        int numberOfInstallments,
        DateTime firstInstallmentDate,
        int? userId = null)
    {
        var sale = await _context.Sales.FindAsync(saleId);
        if (sale == null)
            throw new ArgumentException("البيع غير موجود");

        var installmentAmount = (sale.TotalAmount - sale.DownPayment) / numberOfInstallments;
        var installments = new List<InstallmentPayment>();

        for (int i = 1; i <= numberOfInstallments; i++)
        {
            var installment = new InstallmentPayment
            {
                SaleId = saleId,
                InstallmentNumber = i,
                Amount = installmentAmount,
                DueDate = firstInstallmentDate.AddMonths(i - 1),
                Status = InstallmentStatus.Pending,
                CreatedAt = DateTime.Now,
                CreatedBy = userId
            };

            installments.Add(installment);
        }

        _context.InstallmentPayments.AddRange(installments);
        await _context.SaveChangesAsync();

        return installments;
    }

    public async Task<bool> RecordPaymentAsync(
        int installmentId,
        decimal paidAmount,
        DateTime paymentDate,
        PaymentType paymentType,
        string? notes = null,
        int? userId = null)
    {
        var installment = await GetByIdAsync(installmentId);
        if (installment == null)
            return false;

        installment.PaidAmount = paidAmount;
        installment.PaymentDate = paymentDate;
        installment.PaymentType = paymentType;
        installment.Notes = notes;

        // تحديد الحالة بناءً على المبلغ المدفوع
        if (paidAmount >= installment.Amount)
        {
            installment.Status = InstallmentStatus.Paid;
        }
        else if (paidAmount > 0)
        {
            installment.Status = InstallmentStatus.PartiallyPaid;
        }

        installment.UpdateModificationInfo(userId);
        await UpdateAsync(installment, userId);

        return true;
    }

    public async Task<bool> RecordPartialPaymentAsync(
        int installmentId,
        decimal partialAmount,
        DateTime paymentDate,
        PaymentType paymentType,
        string? notes = null,
        int? userId = null)
    {
        var installment = await GetByIdAsync(installmentId);
        if (installment == null)
            return false;

        installment.PaidAmount += partialAmount;
        installment.PaymentDate = paymentDate;
        installment.PaymentType = paymentType;
        installment.Notes = notes;

        // تحديد الحالة بناءً على المبلغ المدفوع
        if (installment.PaidAmount >= installment.Amount)
        {
            installment.Status = InstallmentStatus.Paid;
        }
        else
        {
            installment.Status = InstallmentStatus.PartiallyPaid;
        }

        installment.UpdateModificationInfo(userId);
        await UpdateAsync(installment, userId);

        return true;
    }

    public async Task<decimal> CalculateLateFeeAsync(int installmentId)
    {
        var installment = await GetByIdAsync(installmentId);
        if (installment == null || installment.DueDate >= DateTime.Today)
            return 0;

        var daysLate = (DateTime.Today - installment.DueDate).Days;
        var lateFeeRate = 0.01m; // 1% يومياً
        var maxLateFeeRate = 0.10m; // حد أقصى 10%

        var lateFee = installment.Amount * lateFeeRate * daysLate;
        var maxLateFee = installment.Amount * maxLateFeeRate;

        return Math.Min(lateFee, maxLateFee);
    }

    public async Task<bool> ApplyLateFeeAsync(int installmentId, int? userId = null)
    {
        var installment = await GetByIdAsync(installmentId);
        if (installment == null)
            return false;

        var lateFee = await CalculateLateFeeAsync(installmentId);
        if (lateFee > 0)
        {
            installment.LateFee = lateFee;
            installment.Status = InstallmentStatus.Overdue;
            installment.UpdateModificationInfo(userId);

            await UpdateAsync(installment, userId);
        }

        return true;
    }

    public async Task<bool> PostponeInstallmentAsync(int installmentId, DateTime newDueDate, string reason, int? userId = null)
    {
        var installment = await GetByIdAsync(installmentId);
        if (installment == null)
            return false;

        installment.DueDate = newDueDate;
        installment.Notes = $"تم التأجيل: {reason}";
        installment.UpdateModificationInfo(userId);

        await UpdateAsync(installment, userId);
        return true;
    }

    public async Task<bool> CancelInstallmentAsync(int installmentId, string reason, int? userId = null)
    {
        var installment = await GetByIdAsync(installmentId);
        if (installment == null)
            return false;

        installment.Status = InstallmentStatus.Cancelled;
        installment.Notes = $"تم الإلغاء: {reason}";
        installment.UpdateModificationInfo(userId);

        await UpdateAsync(installment, userId);
        return true;
    }

    public async Task<IEnumerable<InstallmentPayment>> RescheduleRemainingInstallmentsAsync(
        int saleId,
        List<InstallmentScheduleItem> newSchedule,
        int? userId = null)
    {
        var remainingInstallments = await _dbSet
            .Where(i => i.SaleId == saleId && i.Status == InstallmentStatus.Pending)
            .ToListAsync();

        foreach (var installment in remainingInstallments)
        {
            var newScheduleItem = newSchedule.FirstOrDefault(s => s.InstallmentNumber == installment.InstallmentNumber);
            if (newScheduleItem != null)
            {
                installment.DueDate = newScheduleItem.DueDate;
                installment.Amount = newScheduleItem.Amount;
                installment.UpdateModificationInfo(userId);
            }
        }

        await _context.SaveChangesAsync();
        return remainingInstallments;
    }

    public async Task<decimal> CalculateCustomerTotalDueAsync(int customerId)
    {
        return await _dbSet
            .Where(i => i.Sale.CustomerId == customerId && i.Status != InstallmentStatus.Paid && i.Status != InstallmentStatus.Cancelled)
            .SumAsync(i => i.Amount - i.PaidAmount + i.LateFee);
    }

    public async Task<decimal> CalculateCustomerTotalPaidAsync(int customerId)
    {
        return await _dbSet
            .Where(i => i.Sale.CustomerId == customerId)
            .SumAsync(i => i.PaidAmount);
    }

    public async Task<CustomerInstallmentReport> GetCustomerInstallmentReportAsync(int customerId)
    {
        var installments = await _dbSet
            .Where(i => i.Sale.CustomerId == customerId)
            .Include(i => i.Sale)
                .ThenInclude(s => s.Customer)
            .ToListAsync();

        var customer = installments.FirstOrDefault()?.Sale.Customer;
        if (customer == null)
            throw new ArgumentException("العميل غير موجود");

        var totalDue = installments.Where(i => i.Status != InstallmentStatus.Cancelled).Sum(i => i.Amount);
        var totalPaid = installments.Sum(i => i.PaidAmount);
        var totalLateFees = installments.Sum(i => i.LateFee);
        var overdueInstallments = installments.Count(i => i.Status == InstallmentStatus.Overdue);
        var paidInstallments = installments.Count(i => i.Status == InstallmentStatus.Paid);

        var lastPayment = installments
            .Where(i => i.PaymentDate.HasValue)
            .OrderByDescending(i => i.PaymentDate)
            .FirstOrDefault();

        var nextDue = installments
            .Where(i => i.Status == InstallmentStatus.Pending && i.DueDate > DateTime.Today)
            .OrderBy(i => i.DueDate)
            .FirstOrDefault();

        return new CustomerInstallmentReport
        {
            CustomerId = customerId,
            CustomerName = $"{customer.FirstName} {customer.LastName}",
            TotalDue = totalDue,
            TotalPaid = totalPaid,
            RemainingBalance = totalDue - totalPaid,
            TotalInstallments = installments.Count(i => i.Status != InstallmentStatus.Cancelled),
            PaidInstallments = paidInstallments,
            OverdueInstallments = overdueInstallments,
            TotalLateFees = totalLateFees,
            LastPaymentDate = lastPayment?.PaymentDate,
            NextDueDate = nextDue?.DueDate
        };
    }

    public async Task<InstallmentStatistics> GetInstallmentStatisticsAsync(DateTime? startDate = null, DateTime? endDate = null)
    {
        var query = _dbSet.AsQueryable();

        if (startDate.HasValue)
            query = query.Where(i => i.CreatedAt >= startDate.Value);

        if (endDate.HasValue)
            query = query.Where(i => i.CreatedAt <= endDate.Value);

        var totalInstallments = await query.CountAsync();
        var paidInstallments = await query.CountAsync(i => i.Status == InstallmentStatus.Paid);
        var pendingInstallments = await query.CountAsync(i => i.Status == InstallmentStatus.Pending);
        var overdueInstallments = await query.CountAsync(i => i.Status == InstallmentStatus.Overdue);

        var totalDueAmount = await query.SumAsync(i => i.Amount);
        var totalPaidAmount = await query.SumAsync(i => i.PaidAmount);
        var totalLateFees = await query.SumAsync(i => i.LateFee);

        var averageInstallmentAmount = totalInstallments > 0 ? totalDueAmount / totalInstallments : 0;
        var onTimePaymentRate = totalInstallments > 0 ? (double)paidInstallments / totalInstallments * 100 : 0;

        var customersWithOverdueInstallments = await query
            .Where(i => i.Status == InstallmentStatus.Overdue)
            .Select(i => i.Sale.CustomerId)
            .Distinct()
            .CountAsync();

        return new InstallmentStatistics
        {
            TotalInstallments = totalInstallments,
            PaidInstallments = paidInstallments,
            PendingInstallments = pendingInstallments,
            OverdueInstallments = overdueInstallments,
            TotalDueAmount = totalDueAmount,
            TotalPaidAmount = totalPaidAmount,
            TotalLateFees = totalLateFees,
            AverageInstallmentAmount = averageInstallmentAmount,
            OnTimePaymentRate = onTimePaymentRate,
            CustomersWithOverdueInstallments = customersWithOverdueInstallments
        };
    }

    public async Task<int> SendInstallmentRemindersAsync(int daysBeforeDue = 3, int? userId = null)
    {
        var reminderDate = DateTime.Today.AddDays(daysBeforeDue);
        var installmentsDue = await _dbSet
            .Where(i => i.DueDate <= reminderDate && i.Status == InstallmentStatus.Pending)
            .Include(i => i.Sale)
                .ThenInclude(s => s.Customer)
            .ToListAsync();

        int remindersSent = 0;

        foreach (var installment in installmentsDue)
        {
            // هنا يمكن إضافة منطق إرسال التذكيرات (SMS, Email, etc.)
            // للآن سنقوم بتسجيل التذكير في الملاحظات
            installment.Notes = $"تم إرسال تذكير في {DateTime.Now:yyyy-MM-dd}";
            installment.UpdateModificationInfo(userId);
            remindersSent++;
        }

        if (remindersSent > 0)
        {
            await _context.SaveChangesAsync();
        }

        return remindersSent;
    }

    public async Task<IEnumerable<InstallmentPayment>> SearchInstallmentsAsync(
        int? customerId = null,
        int? saleId = null,
        InstallmentStatus? status = null,
        DateTime? startDate = null,
        DateTime? endDate = null,
        decimal? minAmount = null,
        decimal? maxAmount = null)
    {
        var query = ApplyIncludes(_dbSet);

        if (customerId.HasValue)
            query = query.Where(i => i.Sale.CustomerId == customerId.Value);

        if (saleId.HasValue)
            query = query.Where(i => i.SaleId == saleId.Value);

        if (status.HasValue)
            query = query.Where(i => i.Status == status.Value);

        if (startDate.HasValue)
            query = query.Where(i => i.DueDate >= startDate.Value);

        if (endDate.HasValue)
            query = query.Where(i => i.DueDate <= endDate.Value);

        if (minAmount.HasValue)
            query = query.Where(i => i.Amount >= minAmount.Value);

        if (maxAmount.HasValue)
            query = query.Where(i => i.Amount <= maxAmount.Value);

        return await query
            .OrderBy(i => i.DueDate)
            .ToListAsync();
    }

    public async Task<int> UpdateOverdueInstallmentsAsync(int? userId = null)
    {
        var overdueInstallments = await _dbSet
            .Where(i => i.DueDate < DateTime.Today && i.Status == InstallmentStatus.Pending)
            .ToListAsync();

        foreach (var installment in overdueInstallments)
        {
            installment.Status = InstallmentStatus.Overdue;

            // حساب وتطبيق الغرامة
            var lateFee = await CalculateLateFeeAsync(installment.Id);
            installment.LateFee = lateFee;

            installment.UpdateModificationInfo(userId);
        }

        if (overdueInstallments.Any())
        {
            await _context.SaveChangesAsync();
        }

        return overdueInstallments.Count;
    }

    protected override async Task<bool> ValidateEntityAsync(InstallmentPayment entity)
    {
        // التحقق من وجود البيع
        var sale = await _context.Sales.FindAsync(entity.SaleId);
        if (sale == null)
            return false;

        // التحقق من صحة البيانات المالية
        if (entity.Amount <= 0)
            return false;

        // التحقق من صحة تاريخ الاستحقاق
        if (entity.DueDate < DateTime.Today.AddDays(-365)) // لا يمكن أن يكون القسط متأخر أكثر من سنة
            return false;

        return true;
    }

    protected override async Task OnAfterAddAsync(InstallmentPayment entity, int? userId = null)
    {
        // تسجيل العملية في سجل التدقيق
        var auditLog = new AuditLog
        {
            TableName = "InstallmentPayments",
            RecordId = entity.Id,
            Action = AuditAction.Insert,
            OldValues = null,
            NewValues = $"قسط جديد رقم {entity.InstallmentNumber} للبيع {entity.SaleId}",
            UserId = userId,
            Timestamp = DateTime.Now
        };

        _context.AuditLogs.Add(auditLog);
        await _context.SaveChangesAsync();

        await base.OnAfterAddAsync(entity, userId);
    }

    protected override async Task OnAfterUpdateAsync(InstallmentPayment entity, int? userId = null)
    {
        // إذا تم دفع القسط، تحديث حالة البيع
        if (entity.Status == InstallmentStatus.Paid)
        {
            var sale = await _context.Sales
                .Include(s => s.InstallmentPayments)
                .FirstOrDefaultAsync(s => s.Id == entity.SaleId);

            if (sale != null)
            {
                var allInstallmentsPaid = sale.InstallmentPayments
                    .All(i => i.Status == InstallmentStatus.Paid || i.Status == InstallmentStatus.Cancelled);

                if (allInstallmentsPaid && sale.Status != SaleStatus.Completed)
                {
                    sale.Status = SaleStatus.Completed;
                    sale.UpdateModificationInfo(userId);
                }
            }
        }

        await base.OnAfterUpdateAsync(entity, userId);
    }
}
