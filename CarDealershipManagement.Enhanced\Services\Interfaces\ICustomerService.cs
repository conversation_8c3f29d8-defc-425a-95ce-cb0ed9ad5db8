using CarDealershipManagement.Enhanced.Models;
using CarDealershipManagement.Enhanced.Enums;

namespace CarDealershipManagement.Enhanced.Services.Interfaces;

/// <summary>
/// واجهة خدمة العملاء
/// </summary>
public interface ICustomerService : IBaseService<Customer>
{
    /// <summary>
    /// البحث في العملاء برقم الهوية
    /// </summary>
    /// <param name="identityNumber">رقم الهوية</param>
    /// <returns>العميل أو null</returns>
    Task<Customer?> GetByIdentityNumberAsync(string identityNumber);

    /// <summary>
    /// البحث في العملاء برقم الهاتف
    /// </summary>
    /// <param name="phone">رقم الهاتف</param>
    /// <returns>العميل أو null</returns>
    Task<Customer?> GetByPhoneAsync(string phone);

    /// <summary>
    /// البحث في العملاء بالبريد الإلكتروني
    /// </summary>
    /// <param name="email">البريد الإلكتروني</param>
    /// <returns>العميل أو null</returns>
    Task<Customer?> GetByEmailAsync(string email);

    /// <summary>
    /// الحصول على العملاء حسب الحالة
    /// </summary>
    /// <param name="status">الحالة</param>
    /// <returns>قائمة العملاء</returns>
    Task<IEnumerable<Customer>> GetByStatusAsync(CustomerStatus status);

    /// <summary>
    /// الحصول على العملاء النشطين
    /// </summary>
    /// <returns>قائمة العملاء النشطين</returns>
    Task<IEnumerable<Customer>> GetActiveCustomersAsync();

    /// <summary>
    /// الحصول على العملاء المميزين (VIP)
    /// </summary>
    /// <returns>قائمة العملاء المميزين</returns>
    Task<IEnumerable<Customer>> GetVipCustomersAsync();

    /// <summary>
    /// الحصول على العملاء حسب مندوب المبيعات
    /// </summary>
    /// <param name="salesAgentId">معرف مندوب المبيعات</param>
    /// <returns>قائمة العملاء</returns>
    Task<IEnumerable<Customer>> GetBySalesAgentAsync(int salesAgentId);

    /// <summary>
    /// البحث في العملاء بمعايير متقدمة
    /// </summary>
    /// <param name="searchTerm">مصطلح البحث</param>
    /// <param name="status">الحالة</param>
    /// <param name="city">المدينة</param>
    /// <param name="salesAgentId">معرف مندوب المبيعات</param>
    /// <param name="isVip">عميل مميز</param>
    /// <returns>قائمة العملاء المطابقة</returns>
    Task<IEnumerable<Customer>> SearchCustomersAsync(
        string? searchTerm = null,
        CustomerStatus? status = null,
        string? city = null,
        int? salesAgentId = null,
        bool? isVip = null);

    /// <summary>
    /// تحديث حالة العميل
    /// </summary>
    /// <param name="customerId">معرف العميل</param>
    /// <param name="status">الحالة الجديدة</param>
    /// <param name="userId">معرف المستخدم</param>
    /// <returns>true إذا تم التحديث بنجاح</returns>
    Task<bool> UpdateCustomerStatusAsync(int customerId, CustomerStatus status, int? userId = null);

    /// <summary>
    /// تحديث حد الائتمان للعميل
    /// </summary>
    /// <param name="customerId">معرف العميل</param>
    /// <param name="creditLimit">حد الائتمان الجديد</param>
    /// <param name="userId">معرف المستخدم</param>
    /// <returns>true إذا تم التحديث بنجاح</returns>
    Task<bool> UpdateCreditLimitAsync(int customerId, decimal creditLimit, int? userId = null);

    /// <summary>
    /// تحديث تقييم العميل
    /// </summary>
    /// <param name="customerId">معرف العميل</param>
    /// <param name="rating">التقييم الجديد</param>
    /// <param name="userId">معرف المستخدم</param>
    /// <returns>true إذا تم التحديث بنجاح</returns>
    Task<bool> UpdateCustomerRatingAsync(int customerId, int rating, int? userId = null);

    /// <summary>
    /// إضافة مستند للعميل
    /// </summary>
    /// <param name="customerId">معرف العميل</param>
    /// <param name="document">بيانات المستند</param>
    /// <param name="userId">معرف المستخدم</param>
    /// <returns>المستند المضاف</returns>
    Task<CustomerDocument> AddCustomerDocumentAsync(int customerId, CustomerDocument document, int? userId = null);

    /// <summary>
    /// الحصول على مستندات العميل
    /// </summary>
    /// <param name="customerId">معرف العميل</param>
    /// <returns>قائمة المستندات</returns>
    Task<IEnumerable<CustomerDocument>> GetCustomerDocumentsAsync(int customerId);

    /// <summary>
    /// حذف مستند العميل
    /// </summary>
    /// <param name="documentId">معرف المستند</param>
    /// <param name="userId">معرف المستخدم</param>
    /// <returns>true إذا تم الحذف بنجاح</returns>
    Task<bool> DeleteCustomerDocumentAsync(int documentId, int? userId = null);

    /// <summary>
    /// إضافة تفاعل مع العميل
    /// </summary>
    /// <param name="customerId">معرف العميل</param>
    /// <param name="interaction">بيانات التفاعل</param>
    /// <param name="userId">معرف المستخدم</param>
    /// <returns>التفاعل المضاف</returns>
    Task<CustomerInteraction> AddCustomerInteractionAsync(int customerId, CustomerInteraction interaction, int? userId = null);

    /// <summary>
    /// الحصول على تفاعلات العميل
    /// </summary>
    /// <param name="customerId">معرف العميل</param>
    /// <returns>قائمة التفاعلات</returns>
    Task<IEnumerable<CustomerInteraction>> GetCustomerInteractionsAsync(int customerId);

    /// <summary>
    /// الحصول على مبيعات العميل
    /// </summary>
    /// <param name="customerId">معرف العميل</param>
    /// <returns>قائمة المبيعات</returns>
    Task<IEnumerable<Sale>> GetCustomerSalesAsync(int customerId);

    /// <summary>
    /// الحصول على أقساط العميل
    /// </summary>
    /// <param name="customerId">معرف العميل</param>
    /// <returns>قائمة الأقساط</returns>
    Task<IEnumerable<InstallmentPayment>> GetCustomerInstallmentsAsync(int customerId);

    /// <summary>
    /// الحصول على الأقساط المستحقة للعميل
    /// </summary>
    /// <param name="customerId">معرف العميل</param>
    /// <returns>قائمة الأقساط المستحقة</returns>
    Task<IEnumerable<InstallmentPayment>> GetCustomerOverdueInstallmentsAsync(int customerId);

    /// <summary>
    /// حساب إجمالي مشتريات العميل
    /// </summary>
    /// <param name="customerId">معرف العميل</param>
    /// <returns>إجمالي المشتريات</returns>
    Task<decimal> CalculateCustomerTotalPurchasesAsync(int customerId);

    /// <summary>
    /// حساب الرصيد الحالي للعميل
    /// </summary>
    /// <param name="customerId">معرف العميل</param>
    /// <returns>الرصيد الحالي</returns>
    Task<decimal> CalculateCustomerCurrentBalanceAsync(int customerId);

    /// <summary>
    /// الحصول على إحصائيات العملاء
    /// </summary>
    /// <returns>إحصائيات العملاء</returns>
    Task<CustomerStatistics> GetCustomerStatisticsAsync();

    /// <summary>
    /// الحصول على العملاء الذين لديهم أقساط متأخرة
    /// </summary>
    /// <returns>قائمة العملاء</returns>
    Task<IEnumerable<Customer>> GetCustomersWithOverdueInstallmentsAsync();

    /// <summary>
    /// الحصول على أفضل العملاء (حسب المشتريات)
    /// </summary>
    /// <param name="count">عدد العملاء</param>
    /// <returns>قائمة أفضل العملاء</returns>
    Task<IEnumerable<Customer>> GetTopCustomersAsync(int count = 10);

    /// <summary>
    /// التحقق من توفر رقم الهوية
    /// </summary>
    /// <param name="identityNumber">رقم الهوية</param>
    /// <param name="excludeCustomerId">معرف العميل المستثنى (للتحديث)</param>
    /// <returns>true إذا كان الرقم متاح</returns>
    Task<bool> IsIdentityNumberAvailableAsync(string identityNumber, int? excludeCustomerId = null);

    /// <summary>
    /// التحقق من توفر رقم الهاتف
    /// </summary>
    /// <param name="phone">رقم الهاتف</param>
    /// <param name="excludeCustomerId">معرف العميل المستثنى (للتحديث)</param>
    /// <returns>true إذا كان الرقم متاح</returns>
    Task<bool> IsPhoneAvailableAsync(string phone, int? excludeCustomerId = null);

    /// <summary>
    /// التحقق من توفر البريد الإلكتروني
    /// </summary>
    /// <param name="email">البريد الإلكتروني</param>
    /// <param name="excludeCustomerId">معرف العميل المستثنى (للتحديث)</param>
    /// <returns>true إذا كان البريد متاح</returns>
    Task<bool> IsEmailAvailableAsync(string email, int? excludeCustomerId = null);
}

/// <summary>
/// إحصائيات العملاء
/// </summary>
public class CustomerStatistics
{
    /// <summary>
    /// العدد الإجمالي للعملاء
    /// </summary>
    public int TotalCustomers { get; set; }

    /// <summary>
    /// عدد العملاء النشطين
    /// </summary>
    public int ActiveCustomers { get; set; }

    /// <summary>
    /// عدد العملاء المميزين
    /// </summary>
    public int VipCustomers { get; set; }

    /// <summary>
    /// عدد العملاء الجدد هذا الشهر
    /// </summary>
    public int NewCustomersThisMonth { get; set; }

    /// <summary>
    /// عدد العملاء الذين لديهم أقساط متأخرة
    /// </summary>
    public int CustomersWithOverdueInstallments { get; set; }

    /// <summary>
    /// إجمالي قيمة مشتريات العملاء
    /// </summary>
    public decimal TotalCustomerPurchases { get; set; }

    /// <summary>
    /// متوسط قيمة مشتريات العميل
    /// </summary>
    public decimal AverageCustomerPurchase { get; set; }

    /// <summary>
    /// أعلى قيمة مشتريات عميل
    /// </summary>
    public decimal HighestCustomerPurchase { get; set; }

    /// <summary>
    /// إجمالي الأرصدة المستحقة
    /// </summary>
    public decimal TotalOutstandingBalance { get; set; }

    /// <summary>
    /// متوسط التقييم
    /// </summary>
    public double AverageRating { get; set; }
}
