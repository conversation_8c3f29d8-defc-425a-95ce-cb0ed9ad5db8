@echo off
title Factory Reset Tool - Car Dealership Management
color 0C

echo.
echo ===============================================
echo           FACTORY RESET TOOL
echo        Car Dealership Management System
echo ===============================================
echo.
echo WARNING: This will permanently delete ALL data!
echo.
echo What will be deleted:
echo - All cars, customers, sales, installments
echo - All users except default developer
echo - All uploaded files
echo - All system settings
echo.
echo ===============================================
echo.

set /p confirm="Are you sure you want to continue? (type YES to confirm): "
if /i not "%confirm%"=="YES" (
    echo.
    echo Operation cancelled.
    echo.
    pause
    exit /b 0
)

echo.
echo ===============================================
echo.
set /p finalconfirm="FINAL CONFIRMATION - Type RESET to proceed: "
if /i not "%finalconfirm%"=="RESET" (
    echo.
    echo Operation cancelled.
    echo.
    pause
    exit /b 0
)

echo.
echo ===============================================
echo           PERFORMING FACTORY RESET
echo ===============================================
echo.

echo Step 1: Stopping application if running...
taskkill /f /im CarDealershipManagement.exe >nul 2>&1

echo Step 2: Deleting database...
if exist "CarDealership.db" (
    del "CarDealership.db" >nul 2>&1
    echo ✓ Database deleted
) else (
    echo ✓ Database not found
)

if exist "CarDealership.db-shm" (
    del "CarDealership.db-shm" >nul 2>&1
)

if exist "CarDealership.db-wal" (
    del "CarDealership.db-wal" >nul 2>&1
)

echo Step 3: Deleting uploaded files...
if exist "Archive" (
    rmdir /s /q "Archive" >nul 2>&1
    echo ✓ Archive folder deleted
) else (
    echo ✓ Archive folder not found
)

echo Step 4: Deleting log files...
if exist "Logs" (
    rmdir /s /q "Logs" >nul 2>&1
    echo ✓ Log files deleted
) else (
    echo ✓ Log files not found
)

echo Step 5: Deleting backup files...
if exist "Backups" (
    rmdir /s /q "Backups" >nul 2>&1
    echo ✓ Backup files deleted
) else (
    echo ✓ Backup files not found
)

echo.
echo ===============================================
echo           FACTORY RESET COMPLETE
echo ===============================================
echo.
echo ✓ All data has been permanently deleted
echo ✓ System has been reset to factory defaults
echo.
echo Default login credentials:
echo Username: amrali
echo Password: braa
echo.
echo The application will create a new database
echo with default settings when you run it next time.
echo.
echo ===============================================
echo.
echo Press any key to exit...
pause >nul

exit /b 0
