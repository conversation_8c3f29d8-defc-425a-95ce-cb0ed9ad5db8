using System.ComponentModel;

namespace CarDealershipManagement.Enhanced.Enums;

/// <summary>
/// حالة السيارة في المعرض
/// </summary>
public enum CarStatus
{
    [Description("متاحة")]
    Available = 1,
    
    [Description("محجوزة")]
    Reserved = 2,
    
    [Description("مباعة")]
    Sold = 3,
    
    [Description("قيد الصيانة")]
    UnderMaintenance = 4,
    
    [Description("غير متاحة")]
    Unavailable = 5
}

/// <summary>
/// حالة العميل
/// </summary>
public enum CustomerStatus
{
    [Description("نشط")]
    Active = 1,
    
    [Description("غير نشط")]
    Inactive = 2,
    
    [Description("محظور")]
    Blocked = 3,
    
    [Description("VIP")]
    VIP = 4
}

/// <summary>
/// حالة المورد
/// </summary>
public enum SupplierStatus
{
    [Description("نشط")]
    Active = 1,
    
    [Description("غير نشط")]
    Inactive = 2,
    
    [Description("محظور")]
    Blocked = 3,
    
    [Description("مفضل")]
    Preferred = 4
}

/// <summary>
/// حالة البيع
/// </summary>
public enum SaleStatus
{
    [Description("مسودة")]
    Draft = 1,
    
    [Description("معلق")]
    Pending = 2,
    
    [Description("مؤكد")]
    Confirmed = 3,
    
    [Description("مكتمل")]
    Completed = 4,
    
    [Description("ملغي")]
    Cancelled = 5
}

/// <summary>
/// حالة القسط
/// </summary>
public enum InstallmentStatus
{
    [Description("معلق")]
    Pending = 1,
    
    [Description("مدفوع")]
    Paid = 2,
    
    [Description("متأخر")]
    Overdue = 3,
    
    [Description("مدفوع جزئياً")]
    PartiallyPaid = 4,
    
    [Description("ملغي")]
    Cancelled = 5
}

/// <summary>
/// نوع الدفع
/// </summary>
public enum PaymentType
{
    [Description("نقدي")]
    Cash = 1,
    
    [Description("شيك")]
    Check = 2,
    
    [Description("تحويل بنكي")]
    BankTransfer = 3,
    
    [Description("بطاقة ائتمان")]
    CreditCard = 4,
    
    [Description("قسط")]
    Installment = 5
}

/// <summary>
/// نوع المستند
/// </summary>
public enum DocumentType
{
    [Description("هوية")]
    Identity = 1,
    
    [Description("رخصة قيادة")]
    DrivingLicense = 2,
    
    [Description("عقد")]
    Contract = 3,
    
    [Description("فاتورة")]
    Invoice = 4,
    
    [Description("إيصال")]
    Receipt = 5,
    
    [Description("صورة")]
    Image = 6,
    
    [Description("أخرى")]
    Other = 7
}

/// <summary>
/// مستوى المستخدم
/// </summary>
public enum UserRole
{
    [Description("مطور")]
    Developer = 1,
    
    [Description("مدير")]
    Admin = 2,
    
    [Description("مدير مبيعات")]
    SalesManager = 3,
    
    [Description("مندوب مبيعات")]
    SalesAgent = 4,
    
    [Description("محاسب")]
    Accountant = 5,
    
    [Description("مستخدم")]
    User = 6
}

/// <summary>
/// نوع التقرير
/// </summary>
public enum ReportType
{
    [Description("مبيعات")]
    Sales = 1,
    
    [Description("مالي")]
    Financial = 2,
    
    [Description("مخزون")]
    Inventory = 3,
    
    [Description("عملاء")]
    Customers = 4,
    
    [Description("موردين")]
    Suppliers = 5,
    
    [Description("أقساط")]
    Installments = 6
}

/// <summary>
/// صيغة التقرير
/// </summary>
public enum ReportFormat
{
    [Description("PDF")]
    PDF = 1,
    
    [Description("Excel")]
    Excel = 2,
    
    [Description("Word")]
    Word = 3,
    
    [Description("HTML")]
    HTML = 4
}

/// <summary>
/// نوع العملية في سجل التدقيق
/// </summary>
public enum AuditAction
{
    [Description("إنشاء")]
    Create = 1,
    
    [Description("تحديث")]
    Update = 2,
    
    [Description("حذف")]
    Delete = 3,
    
    [Description("عرض")]
    View = 4,
    
    [Description("تسجيل دخول")]
    Login = 5,
    
    [Description("تسجيل خروج")]
    Logout = 6,
    
    [Description("نسخ احتياطي")]
    Backup = 7,
    
    [Description("استعادة")]
    Restore = 8
}

/// <summary>
/// نوع الترخيص
/// </summary>
public enum LicenseType
{
    [Description("تجريبي")]
    Trial = 1,
    
    [Description("شهري")]
    Monthly = 2,
    
    [Description("سنوي")]
    Yearly = 3,
    
    [Description("دائم")]
    Permanent = 4
}

/// <summary>
/// حالة الترخيص
/// </summary>
public enum LicenseStatus
{
    [Description("نشط")]
    Active = 1,
    
    [Description("منتهي الصلاحية")]
    Expired = 2,
    
    [Description("معلق")]
    Suspended = 3,
    
    [Description("ملغي")]
    Cancelled = 4
}
