using Microsoft.Extensions.DependencyInjection;

namespace CarDealershipManagement.Enhanced.Forms;

// نماذج مؤقتة للوحدات الأخرى

public partial class CustomersForm : Form
{
    private readonly IServiceProvider _serviceProvider;
    private Label lblStatus;

    public CustomersForm(IServiceProvider serviceProvider)
    {
        _serviceProvider = serviceProvider;
        InitializeComponent();
    }

    private void InitializeComponent()
    {
        lblStatus = new Label();
        SuspendLayout();
        
        lblStatus.AutoSize = true;
        lblStatus.Font = new Font("Segoe UI", 12F);
        lblStatus.Location = new Point(50, 50);
        lblStatus.Name = "lblStatus";
        lblStatus.Size = new Size(200, 28);
        lblStatus.TabIndex = 0;
        lblStatus.Text = "وحدة إدارة العملاء";
        
        AutoScaleDimensions = new SizeF(8F, 20F);
        AutoScaleMode = AutoScaleMode.Font;
        ClientSize = new Size(800, 600);
        Controls.Add(lblStatus);
        Name = "CustomersForm";
        RightToLeft = RightToLeft.Yes;
        Text = "إدارة العملاء";
        ResumeLayout(false);
        PerformLayout();
    }
}

public partial class SuppliersForm : Form
{
    private readonly IServiceProvider _serviceProvider;
    private Label lblStatus;

    public SuppliersForm(IServiceProvider serviceProvider)
    {
        _serviceProvider = serviceProvider;
        InitializeComponent();
    }

    private void InitializeComponent()
    {
        lblStatus = new Label();
        SuspendLayout();
        
        lblStatus.AutoSize = true;
        lblStatus.Font = new Font("Segoe UI", 12F);
        lblStatus.Location = new Point(50, 50);
        lblStatus.Name = "lblStatus";
        lblStatus.Size = new Size(200, 28);
        lblStatus.TabIndex = 0;
        lblStatus.Text = "وحدة إدارة الموردين";
        
        AutoScaleDimensions = new SizeF(8F, 20F);
        AutoScaleMode = AutoScaleMode.Font;
        ClientSize = new Size(800, 600);
        Controls.Add(lblStatus);
        Name = "SuppliersForm";
        RightToLeft = RightToLeft.Yes;
        Text = "إدارة الموردين";
        ResumeLayout(false);
        PerformLayout();
    }
}

public partial class SalesForm : Form
{
    private readonly IServiceProvider _serviceProvider;
    private Label lblStatus;

    public SalesForm(IServiceProvider serviceProvider)
    {
        _serviceProvider = serviceProvider;
        InitializeComponent();
    }

    private void InitializeComponent()
    {
        lblStatus = new Label();
        SuspendLayout();
        
        lblStatus.AutoSize = true;
        lblStatus.Font = new Font("Segoe UI", 12F);
        lblStatus.Location = new Point(50, 50);
        lblStatus.Name = "lblStatus";
        lblStatus.Size = new Size(200, 28);
        lblStatus.TabIndex = 0;
        lblStatus.Text = "وحدة إدارة المبيعات";
        
        AutoScaleDimensions = new SizeF(8F, 20F);
        AutoScaleMode = AutoScaleMode.Font;
        ClientSize = new Size(800, 600);
        Controls.Add(lblStatus);
        Name = "SalesForm";
        RightToLeft = RightToLeft.Yes;
        Text = "إدارة المبيعات";
        ResumeLayout(false);
        PerformLayout();
    }
}

public partial class InstallmentsForm : Form
{
    private readonly IServiceProvider _serviceProvider;
    private Label lblStatus;

    public InstallmentsForm(IServiceProvider serviceProvider)
    {
        _serviceProvider = serviceProvider;
        InitializeComponent();
    }

    private void InitializeComponent()
    {
        lblStatus = new Label();
        SuspendLayout();
        
        lblStatus.AutoSize = true;
        lblStatus.Font = new Font("Segoe UI", 12F);
        lblStatus.Location = new Point(50, 50);
        lblStatus.Name = "lblStatus";
        lblStatus.Size = new Size(200, 28);
        lblStatus.TabIndex = 0;
        lblStatus.Text = "وحدة إدارة الأقساط";
        
        AutoScaleDimensions = new SizeF(8F, 20F);
        AutoScaleMode = AutoScaleMode.Font;
        ClientSize = new Size(800, 600);
        Controls.Add(lblStatus);
        Name = "InstallmentsForm";
        RightToLeft = RightToLeft.Yes;
        Text = "إدارة الأقساط";
        ResumeLayout(false);
        PerformLayout();
    }
}

public partial class ReportsForm : Form
{
    private readonly IServiceProvider _serviceProvider;
    private Label lblStatus;

    public ReportsForm(IServiceProvider serviceProvider)
    {
        _serviceProvider = serviceProvider;
        InitializeComponent();
    }

    private void InitializeComponent()
    {
        lblStatus = new Label();
        SuspendLayout();
        
        lblStatus.AutoSize = true;
        lblStatus.Font = new Font("Segoe UI", 12F);
        lblStatus.Location = new Point(50, 50);
        lblStatus.Name = "lblStatus";
        lblStatus.Size = new Size(200, 28);
        lblStatus.TabIndex = 0;
        lblStatus.Text = "وحدة التقارير";
        
        AutoScaleDimensions = new SizeF(8F, 20F);
        AutoScaleMode = AutoScaleMode.Font;
        ClientSize = new Size(800, 600);
        Controls.Add(lblStatus);
        Name = "ReportsForm";
        RightToLeft = RightToLeft.Yes;
        Text = "التقارير";
        ResumeLayout(false);
        PerformLayout();
    }
}

public partial class UsersForm : Form
{
    private readonly IServiceProvider _serviceProvider;
    private Label lblStatus;

    public UsersForm(IServiceProvider serviceProvider)
    {
        _serviceProvider = serviceProvider;
        InitializeComponent();
    }

    private void InitializeComponent()
    {
        lblStatus = new Label();
        SuspendLayout();
        
        lblStatus.AutoSize = true;
        lblStatus.Font = new Font("Segoe UI", 12F);
        lblStatus.Location = new Point(50, 50);
        lblStatus.Name = "lblStatus";
        lblStatus.Size = new Size(200, 28);
        lblStatus.TabIndex = 0;
        lblStatus.Text = "وحدة إدارة المستخدمين";
        
        AutoScaleDimensions = new SizeF(8F, 20F);
        AutoScaleMode = AutoScaleMode.Font;
        ClientSize = new Size(800, 600);
        Controls.Add(lblStatus);
        Name = "UsersForm";
        RightToLeft = RightToLeft.Yes;
        Text = "إدارة المستخدمين";
        ResumeLayout(false);
        PerformLayout();
    }
}
