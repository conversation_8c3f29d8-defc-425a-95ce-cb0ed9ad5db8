using Microsoft.EntityFrameworkCore;
using BCrypt.Net;
using CarDealershipManagement.Enhanced.Data;
using CarDealershipManagement.Enhanced.Models;
using CarDealershipManagement.Enhanced.Enums;
using CarDealershipManagement.Enhanced.Services.Interfaces;
using System.Text.RegularExpressions;

namespace CarDealershipManagement.Enhanced.Services;

/// <summary>
/// خدمة المستخدمين
/// </summary>
public class UserService : BaseService<User>, IUserService
{
    public UserService(CarDealershipDbContext context) : base(context)
    {
    }

    protected override IQueryable<User> ApplyIncludes(IQueryable<User> query)
    {
        return query
            .Include(u => u.Manager)
            .Include(u => u.UserPermissions)
            .Include(u => u.Sales)
            .Include(u => u.Customers)
            .Include(u => u.UserLoginLogs);
    }

    public async Task<User?> LoginAsync(string username, string password)
    {
        var user = await _dbSet
            .Include(u => u.UserPermissions)
            .FirstOrDefaultAsync(u => u.Username == username && !u.IsDeleted);

        if (user == null || !BCrypt.Net.BCrypt.Verify(password, user.PasswordHash))
            return null;

        // التحقق من حالة المستخدم
        if (!user.IsActive || user.IsLocked)
            return null;

        // تحديث آخر تسجيل دخول
        user.LastLoginDate = DateTime.Now;
        user.LoginAttempts = 0; // إعادة تعيين محاولات تسجيل الدخول
        user.UpdateModificationInfo();

        // تسجيل عملية تسجيل الدخول
        var loginLog = new UserLoginLog
        {
            UserId = user.Id,
            LoginTime = DateTime.Now,
            IPAddress = "127.0.0.1", // يمكن تمرير IP الفعلي
            IsSuccessful = true,
            CreatedAt = DateTime.Now
        };

        _context.UserLoginLogs.Add(loginLog);
        await _context.SaveChangesAsync();

        return user;
    }

    public async Task<bool> LogoutAsync(int userId)
    {
        var user = await GetByIdAsync(userId);
        if (user == null)
            return false;

        // تسجيل عملية تسجيل الخروج
        var loginLog = await _context.UserLoginLogs
            .Where(l => l.UserId == userId && l.LogoutTime == null)
            .OrderByDescending(l => l.LoginTime)
            .FirstOrDefaultAsync();

        if (loginLog != null)
        {
            loginLog.LogoutTime = DateTime.Now;
            await _context.SaveChangesAsync();
        }

        return true;
    }

    public async Task<User?> GetByUsernameAsync(string username)
    {
        return await ApplyIncludes(_dbSet)
            .FirstOrDefaultAsync(u => u.Username == username);
    }

    public async Task<User?> GetByEmailAsync(string email)
    {
        return await ApplyIncludes(_dbSet)
            .FirstOrDefaultAsync(u => u.Email == email);
    }

    public async Task<IEnumerable<User>> GetByRoleAsync(UserRole role)
    {
        return await ApplyIncludes(_dbSet)
            .Where(u => u.Role == role)
            .ToListAsync();
    }

    public async Task<IEnumerable<User>> GetActiveUsersAsync()
    {
        return await ApplyIncludes(_dbSet)
            .Where(u => u.IsActive && !u.IsLocked)
            .ToListAsync();
    }

    public async Task<IEnumerable<User>> GetLockedUsersAsync()
    {
        return await ApplyIncludes(_dbSet)
            .Where(u => u.IsLocked)
            .ToListAsync();
    }

    public async Task<IEnumerable<User>> GetByManagerAsync(int managerId)
    {
        return await ApplyIncludes(_dbSet)
            .Where(u => u.ManagerId == managerId)
            .ToListAsync();
    }

    public async Task<bool> ChangePasswordAsync(int userId, string currentPassword, string newPassword)
    {
        var user = await GetByIdAsync(userId);
        if (user == null)
            return false;

        // التحقق من كلمة المرور الحالية
        if (!BCrypt.Net.BCrypt.Verify(currentPassword, user.PasswordHash))
            return false;

        // التحقق من قوة كلمة المرور الجديدة
        var strengthResult = await CheckPasswordStrengthAsync(newPassword);
        if (!strengthResult.IsStrong)
            return false;

        // تحديث كلمة المرور
        user.PasswordHash = BCrypt.Net.BCrypt.HashPassword(newPassword);
        user.PasswordChangedDate = DateTime.Now;
        user.UpdateModificationInfo(userId);

        await UpdateAsync(user, userId);
        return true;
    }

    public async Task<bool> ResetPasswordAsync(int userId, string newPassword, int adminUserId)
    {
        var user = await GetByIdAsync(userId);
        if (user == null)
            return false;

        // التحقق من قوة كلمة المرور الجديدة
        var strengthResult = await CheckPasswordStrengthAsync(newPassword);
        if (!strengthResult.IsStrong)
            return false;

        // تحديث كلمة المرور
        user.PasswordHash = BCrypt.Net.BCrypt.HashPassword(newPassword);
        user.PasswordChangedDate = DateTime.Now;
        user.MustChangePassword = true; // إجبار المستخدم على تغيير كلمة المرور
        user.UpdateModificationInfo(adminUserId);

        await UpdateAsync(user, adminUserId);
        return true;
    }

    public async Task<bool> LockUserAsync(int userId, string reason, int adminUserId)
    {
        var user = await GetByIdAsync(userId);
        if (user == null)
            return false;

        user.IsLocked = true;
        user.LockReason = reason;
        user.LockedDate = DateTime.Now;
        user.UpdateModificationInfo(adminUserId);

        await UpdateAsync(user, adminUserId);
        return true;
    }

    public async Task<bool> UnlockUserAsync(int userId, int adminUserId)
    {
        var user = await GetByIdAsync(userId);
        if (user == null)
            return false;

        user.IsLocked = false;
        user.LockReason = null;
        user.LockedDate = null;
        user.LoginAttempts = 0;
        user.UpdateModificationInfo(adminUserId);

        await UpdateAsync(user, adminUserId);
        return true;
    }

    public async Task<UserPermission> AddUserPermissionAsync(int userId, UserPermission permission, int adminUserId)
    {
        permission.UserId = userId;
        permission.CreatedAt = DateTime.Now;
        permission.CreatedBy = adminUserId;

        _context.UserPermissions.Add(permission);
        await _context.SaveChangesAsync();

        return permission;
    }

    public async Task<bool> RemoveUserPermissionAsync(int userId, string permissionName, int adminUserId)
    {
        var permission = await _context.UserPermissions
            .FirstOrDefaultAsync(p => p.UserId == userId && p.PermissionName == permissionName);

        if (permission == null)
            return false;

        permission.SoftDelete(adminUserId);
        await _context.SaveChangesAsync();

        return true;
    }

    public async Task<IEnumerable<UserPermission>> GetUserPermissionsAsync(int userId)
    {
        return await _context.UserPermissions
            .Where(p => p.UserId == userId)
            .ToListAsync();
    }

    public async Task<bool> HasPermissionAsync(int userId, string permissionName)
    {
        return await _context.UserPermissions
            .AnyAsync(p => p.UserId == userId && p.PermissionName == permissionName && p.IsActive);
    }

    public async Task<IEnumerable<UserLoginLog>> GetUserLoginLogsAsync(int userId, DateTime? startDate = null, DateTime? endDate = null)
    {
        var query = _context.UserLoginLogs.Where(l => l.UserId == userId);

        if (startDate.HasValue)
            query = query.Where(l => l.LoginTime >= startDate.Value);

        if (endDate.HasValue)
            query = query.Where(l => l.LoginTime <= endDate.Value);

        return await query
            .OrderByDescending(l => l.LoginTime)
            .ToListAsync();
    }

    public async Task<IEnumerable<Sale>> GetUserSalesAsync(int userId, DateTime? startDate = null, DateTime? endDate = null)
    {
        var query = _context.Sales.Where(s => s.SalesAgentId == userId);

        if (startDate.HasValue)
            query = query.Where(s => s.SaleDate >= startDate.Value);

        if (endDate.HasValue)
            query = query.Where(s => s.SaleDate <= endDate.Value);

        return await query
            .Include(s => s.Car)
            .Include(s => s.Customer)
            .OrderByDescending(s => s.SaleDate)
            .ToListAsync();
    }

    public async Task<IEnumerable<Customer>> GetUserCustomersAsync(int userId)
    {
        return await _context.Customers
            .Where(c => c.SalesAgentId == userId)
            .OrderBy(c => c.FirstName)
            .ThenBy(c => c.LastName)
            .ToListAsync();
    }

    public async Task<decimal> CalculateUserCommissionsAsync(int userId, DateTime? startDate = null, DateTime? endDate = null)
    {
        var query = _context.Sales.Where(s => s.SalesAgentId == userId && s.Status == SaleStatus.Confirmed);

        if (startDate.HasValue)
            query = query.Where(s => s.SaleDate >= startDate.Value);

        if (endDate.HasValue)
            query = query.Where(s => s.SaleDate <= endDate.Value);

        var totalProfit = await query.SumAsync(s => s.ProfitAmount);
        return totalProfit * 0.05m; // 5% عمولة
    }

    public async Task<UserStatistics> GetUserStatisticsAsync()
    {
        var totalUsers = await _dbSet.CountAsync();
        var activeUsers = await _dbSet.CountAsync(u => u.IsActive && !u.IsLocked);
        var lockedUsers = await _dbSet.CountAsync(u => u.IsLocked);
        var administrators = await _dbSet.CountAsync(u => u.Role == UserRole.Administrator);
        var salesAgents = await _dbSet.CountAsync(u => u.Role == UserRole.SalesAgent);
        var employees = await _dbSet.CountAsync(u => u.Role == UserRole.Employee);

        var newUsersThisMonth = await _dbSet.CountAsync(u => 
            u.CreatedAt.Month == DateTime.Now.Month && u.CreatedAt.Year == DateTime.Now.Year);

        var loginsToday = await _context.UserLoginLogs.CountAsync(l => 
            l.LoginTime.Date == DateTime.Today && l.IsSuccessful);

        var onlineUsers = await _context.UserLoginLogs
            .Where(l => l.LogoutTime == null && l.LoginTime >= DateTime.Now.AddHours(-8))
            .Select(l => l.UserId)
            .Distinct()
            .CountAsync();

        var averageDailyLogins = await _context.UserLoginLogs
            .Where(l => l.IsSuccessful && l.LoginTime >= DateTime.Now.AddDays(-30))
            .GroupBy(l => l.LoginTime.Date)
            .AverageAsync(g => g.Count());

        return new UserStatistics
        {
            TotalUsers = totalUsers,
            ActiveUsers = activeUsers,
            LockedUsers = lockedUsers,
            Administrators = administrators,
            SalesAgents = salesAgents,
            Employees = employees,
            NewUsersThisMonth = newUsersThisMonth,
            LoginsToday = loginsToday,
            OnlineUsers = onlineUsers,
            AverageDailyLogins = averageDailyLogins
        };
    }

    public async Task<IEnumerable<User>> SearchUsersAsync(
        string? searchTerm = null,
        UserRole? role = null,
        bool? isActive = null,
        int? managerId = null)
    {
        var query = ApplyIncludes(_dbSet);

        if (!string.IsNullOrWhiteSpace(searchTerm))
        {
            query = query.Where(u =>
                u.FirstName.Contains(searchTerm) ||
                u.LastName.Contains(searchTerm) ||
                u.Username.Contains(searchTerm) ||
                u.Email.Contains(searchTerm) ||
                u.Phone.Contains(searchTerm));
        }

        if (role.HasValue)
            query = query.Where(u => u.Role == role.Value);

        if (isActive.HasValue)
            query = query.Where(u => u.IsActive == isActive.Value);

        if (managerId.HasValue)
            query = query.Where(u => u.ManagerId == managerId.Value);

        return await query.ToListAsync();
    }

    public async Task<bool> IsUsernameAvailableAsync(string username, int? excludeUserId = null)
    {
        var query = _dbSet.Where(u => u.Username == username);

        if (excludeUserId.HasValue)
            query = query.Where(u => u.Id != excludeUserId.Value);

        return !await query.AnyAsync();
    }

    public async Task<bool> IsEmailAvailableAsync(string email, int? excludeUserId = null)
    {
        var query = _dbSet.Where(u => u.Email == email);

        if (excludeUserId.HasValue)
            query = query.Where(u => u.Id != excludeUserId.Value);

        return !await query.AnyAsync();
    }

    public async Task<PasswordStrengthResult> CheckPasswordStrengthAsync(string password)
    {
        var result = new PasswordStrengthResult();
        var recommendations = new List<string>();

        // التحقق من الطول
        if (password.Length < 8)
        {
            recommendations.Add("يجب أن تكون كلمة المرور 8 أحرف على الأقل");
        }
        else
        {
            result.Score += 1;
        }

        // التحقق من وجود أحرف كبيرة
        if (!Regex.IsMatch(password, @"[A-Z]"))
        {
            recommendations.Add("يجب أن تحتوي على حرف كبير واحد على الأقل");
        }
        else
        {
            result.Score += 1;
        }

        // التحقق من وجود أحرف صغيرة
        if (!Regex.IsMatch(password, @"[a-z]"))
        {
            recommendations.Add("يجب أن تحتوي على حرف صغير واحد على الأقل");
        }
        else
        {
            result.Score += 1;
        }

        // التحقق من وجود أرقام
        if (!Regex.IsMatch(password, @"[0-9]"))
        {
            recommendations.Add("يجب أن تحتوي على رقم واحد على الأقل");
        }
        else
        {
            result.Score += 1;
        }

        // التحقق من وجود رموز خاصة
        if (!Regex.IsMatch(password, @"[!@#$%^&*(),.?""':;{}|<>]"))
        {
            recommendations.Add("يجب أن تحتوي على رمز خاص واحد على الأقل");
        }
        else
        {
            result.Score += 1;
        }

        result.MaxScore = 5;
        result.StrengthLevel = result.Score;
        result.IsStrong = result.Score >= 4;
        result.Recommendations = recommendations;

        result.StrengthDescription = result.Score switch
        {
            1 => "ضعيفة جداً",
            2 => "ضعيفة",
            3 => "متوسطة",
            4 => "قوية",
            5 => "قوية جداً",
            _ => "غير صالحة"
        };

        return result;
    }

    public async Task<string> GenerateStrongPasswordAsync(int length = 12)
    {
        const string upperCase = "ABCDEFGHIJKLMNOPQRSTUVWXYZ";
        const string lowerCase = "abcdefghijklmnopqrstuvwxyz";
        const string digits = "0123456789";
        const string specialChars = "!@#$%^&*()";

        var random = new Random();
        var password = new List<char>();

        // ضمان وجود حرف واحد على الأقل من كل نوع
        password.Add(upperCase[random.Next(upperCase.Length)]);
        password.Add(lowerCase[random.Next(lowerCase.Length)]);
        password.Add(digits[random.Next(digits.Length)]);
        password.Add(specialChars[random.Next(specialChars.Length)]);

        // ملء باقي الطول
        var allChars = upperCase + lowerCase + digits + specialChars;
        for (int i = 4; i < length; i++)
        {
            password.Add(allChars[random.Next(allChars.Length)]);
        }

        // خلط الأحرف
        for (int i = password.Count - 1; i > 0; i--)
        {
            int j = random.Next(i + 1);
            (password[i], password[j]) = (password[j], password[i]);
        }

        return new string(password.ToArray());
    }

    protected override async Task<bool> ValidateEntityAsync(User entity)
    {
        // التحقق من عدم تكرار اسم المستخدم
        if (!await IsUsernameAvailableAsync(entity.Username, entity.Id))
            return false;

        // التحقق من عدم تكرار البريد الإلكتروني
        if (!string.IsNullOrWhiteSpace(entity.Email) && !await IsEmailAvailableAsync(entity.Email, entity.Id))
            return false;

        // التحقق من صحة البيانات
        if (string.IsNullOrWhiteSpace(entity.FirstName) || string.IsNullOrWhiteSpace(entity.LastName))
            return false;

        return true;
    }

    protected override async Task OnAfterAddAsync(User entity, int? userId = null)
    {
        // تسجيل العملية في سجل التدقيق
        var auditLog = new AuditLog
        {
            TableName = "Users",
            RecordId = entity.Id,
            Action = AuditAction.Insert,
            OldValues = null,
            NewValues = $"مستخدم جديد: {entity.Username}",
            UserId = userId,
            Timestamp = DateTime.Now
        };

        _context.AuditLogs.Add(auditLog);
        await _context.SaveChangesAsync();

        await base.OnAfterAddAsync(entity, userId);
    }
}
