using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using CarDealershipManagement.Enhanced.Enums;

namespace CarDealershipManagement.Enhanced.Models;

/// <summary>
/// سجل الصيانة للسيارات
/// </summary>
[Table("MaintenanceRecords")]
public class MaintenanceRecord : BaseEntity
{
    /// <summary>
    /// معرف السيارة
    /// </summary>
    [Required(ErrorMessage = "السيارة مطلوبة")]
    [Column("CarId")]
    public int CarId { get; set; }

    /// <summary>
    /// تاريخ الصيانة
    /// </summary>
    [Required]
    [Column("MaintenanceDate")]
    public DateTime MaintenanceDate { get; set; } = DateTime.Now;

    /// <summary>
    /// نوع الصيانة
    /// </summary>
    [Required(ErrorMessage = "نوع الصيانة مطلوب")]
    [StringLength(200, ErrorMessage = "نوع الصيانة يجب أن يكون أقل من 200 حرف")]
    [Column("MaintenanceType")]
    public string MaintenanceType { get; set; } = string.Empty;

    /// <summary>
    /// الوصف
    /// </summary>
    [StringLength(1000)]
    [Column("Description")]
    public string? Description { get; set; }

    /// <summary>
    /// التكلفة
    /// </summary>
    [Range(0, double.MaxValue, ErrorMessage = "التكلفة يجب أن تكون أكبر من أو تساوي صفر")]
    [Column("Cost", TypeName = "decimal(18,2)")]
    public decimal Cost { get; set; } = 0;

    /// <summary>
    /// مقدم الخدمة
    /// </summary>
    [StringLength(200)]
    [Column("ServiceProvider")]
    public string? ServiceProvider { get; set; }

    /// <summary>
    /// تاريخ الصيانة القادمة
    /// </summary>
    [Column("NextMaintenanceDate")]
    public DateTime? NextMaintenanceDate { get; set; }

    /// <summary>
    /// الكيلومترات عند الصيانة
    /// </summary>
    [Column("MileageAtMaintenance")]
    public int? MileageAtMaintenance { get; set; }

    /// <summary>
    /// السيارة
    /// </summary>
    [ForeignKey("CarId")]
    public virtual Car Car { get; set; } = null!;
}

/// <summary>
/// تفاعلات العميل
/// </summary>
[Table("CustomerInteractions")]
public class CustomerInteraction : BaseEntity
{
    /// <summary>
    /// معرف العميل
    /// </summary>
    [Required(ErrorMessage = "العميل مطلوب")]
    [Column("CustomerId")]
    public int CustomerId { get; set; }

    /// <summary>
    /// معرف المستخدم
    /// </summary>
    [Required(ErrorMessage = "المستخدم مطلوب")]
    [Column("UserId")]
    public int UserId { get; set; }

    /// <summary>
    /// نوع التفاعل
    /// </summary>
    [Required(ErrorMessage = "نوع التفاعل مطلوب")]
    [StringLength(100, ErrorMessage = "نوع التفاعل يجب أن يكون أقل من 100 حرف")]
    [Column("InteractionType")]
    public string InteractionType { get; set; } = string.Empty;

    /// <summary>
    /// تاريخ التفاعل
    /// </summary>
    [Required]
    [Column("InteractionDate")]
    public DateTime InteractionDate { get; set; } = DateTime.Now;

    /// <summary>
    /// الموضوع
    /// </summary>
    [StringLength(200)]
    [Column("Subject")]
    public string? Subject { get; set; }

    /// <summary>
    /// التفاصيل
    /// </summary>
    [StringLength(2000)]
    [Column("Details")]
    public string? Details { get; set; }

    /// <summary>
    /// النتيجة
    /// </summary>
    [StringLength(1000)]
    [Column("Outcome")]
    public string? Outcome { get; set; }

    /// <summary>
    /// تاريخ المتابعة
    /// </summary>
    [Column("FollowUpDate")]
    public DateTime? FollowUpDate { get; set; }

    /// <summary>
    /// الأولوية
    /// </summary>
    [StringLength(20)]
    [Column("Priority")]
    public string? Priority { get; set; } = "متوسط";

    /// <summary>
    /// الحالة
    /// </summary>
    [StringLength(50)]
    [Column("Status")]
    public string? Status { get; set; } = "مفتوح";

    /// <summary>
    /// العميل
    /// </summary>
    [ForeignKey("CustomerId")]
    public virtual Customer Customer { get; set; } = null!;

    /// <summary>
    /// المستخدم
    /// </summary>
    [ForeignKey("UserId")]
    public virtual User User { get; set; } = null!;
}

/// <summary>
/// عقود المورد
/// </summary>
[Table("SupplierContracts")]
public class SupplierContract : BaseEntity
{
    /// <summary>
    /// معرف المورد
    /// </summary>
    [Required(ErrorMessage = "المورد مطلوب")]
    [Column("SupplierId")]
    public int SupplierId { get; set; }

    /// <summary>
    /// رقم العقد
    /// </summary>
    [Required(ErrorMessage = "رقم العقد مطلوب")]
    [StringLength(100, ErrorMessage = "رقم العقد يجب أن يكون أقل من 100 حرف")]
    [Column("ContractNumber")]
    public string ContractNumber { get; set; } = string.Empty;

    /// <summary>
    /// عنوان العقد
    /// </summary>
    [Required(ErrorMessage = "عنوان العقد مطلوب")]
    [StringLength(200, ErrorMessage = "عنوان العقد يجب أن يكون أقل من 200 حرف")]
    [Column("Title")]
    public string Title { get; set; } = string.Empty;

    /// <summary>
    /// تاريخ بداية العقد
    /// </summary>
    [Required]
    [Column("StartDate")]
    public DateTime StartDate { get; set; }

    /// <summary>
    /// تاريخ انتهاء العقد
    /// </summary>
    [Column("EndDate")]
    public DateTime? EndDate { get; set; }

    /// <summary>
    /// قيمة العقد
    /// </summary>
    [Range(0, double.MaxValue, ErrorMessage = "قيمة العقد يجب أن تكون أكبر من أو تساوي صفر")]
    [Column("ContractValue", TypeName = "decimal(18,2)")]
    public decimal ContractValue { get; set; } = 0;

    /// <summary>
    /// العملة
    /// </summary>
    [StringLength(10)]
    [Column("Currency")]
    public string? Currency { get; set; } = "SAR";

    /// <summary>
    /// الشروط والأحكام
    /// </summary>
    [Column("TermsAndConditions")]
    public string? TermsAndConditions { get; set; }

    /// <summary>
    /// حالة العقد
    /// </summary>
    [StringLength(50)]
    [Column("Status")]
    public string? Status { get; set; } = "نشط";

    /// <summary>
    /// ملاحظات
    /// </summary>
    [StringLength(2000)]
    [Column("Notes")]
    public new string? Notes { get; set; }

    /// <summary>
    /// المورد
    /// </summary>
    [ForeignKey("SupplierId")]
    public virtual Supplier Supplier { get; set; } = null!;

    /// <summary>
    /// هل العقد نشط
    /// </summary>
    [NotMapped]
    public bool IsActive => Status == "نشط" && (!EndDate.HasValue || EndDate.Value >= DateTime.Now);

    /// <summary>
    /// عدد أيام حتى انتهاء العقد
    /// </summary>
    [NotMapped]
    public int? DaysUntilExpiry => EndDate.HasValue ? (EndDate.Value - DateTime.Now).Days : null;
}

/// <summary>
/// النسخ الاحتياطية
/// </summary>
[Table("BackupRecords")]
public class BackupRecord : BaseEntity
{
    /// <summary>
    /// اسم النسخة الاحتياطية
    /// </summary>
    [Required(ErrorMessage = "اسم النسخة الاحتياطية مطلوب")]
    [StringLength(255, ErrorMessage = "اسم النسخة الاحتياطية يجب أن يكون أقل من 255 حرف")]
    [Column("BackupName")]
    public string BackupName { get; set; } = string.Empty;

    /// <summary>
    /// مسار الملف
    /// </summary>
    [Required(ErrorMessage = "مسار الملف مطلوب")]
    [StringLength(500, ErrorMessage = "مسار الملف يجب أن يكون أقل من 500 حرف")]
    [Column("FilePath")]
    public string FilePath { get; set; } = string.Empty;

    /// <summary>
    /// حجم الملف بالبايت
    /// </summary>
    [Range(0, long.MaxValue, ErrorMessage = "حجم الملف يجب أن يكون أكبر من أو يساوي صفر")]
    [Column("FileSize")]
    public long FileSize { get; set; }

    /// <summary>
    /// تاريخ النسخة الاحتياطية
    /// </summary>
    [Required]
    [Column("BackupDate")]
    public DateTime BackupDate { get; set; } = DateTime.Now;

    /// <summary>
    /// نوع النسخة الاحتياطية
    /// </summary>
    [StringLength(50)]
    [Column("BackupType")]
    public string? BackupType { get; set; } = "تلقائي";

    /// <summary>
    /// الوصف
    /// </summary>
    [StringLength(500)]
    [Column("Description")]
    public string? Description { get; set; }

    /// <summary>
    /// هل النسخة مضغوطة
    /// </summary>
    [Column("IsCompressed")]
    public bool IsCompressed { get; set; } = false;

    /// <summary>
    /// هل النسخة مشفرة
    /// </summary>
    [Column("IsEncrypted")]
    public bool IsEncrypted { get; set; } = false;

    /// <summary>
    /// معرف المستخدم الذي أنشأ النسخة
    /// </summary>
    [Column("CreatedByUserId")]
    public int? CreatedByUserId { get; set; }

    /// <summary>
    /// حالة النسخة الاحتياطية
    /// </summary>
    [StringLength(50)]
    [Column("Status")]
    public string? Status { get; set; } = "مكتملة";

    /// <summary>
    /// رسالة الخطأ (في حالة الفشل)
    /// </summary>
    [StringLength(1000)]
    [Column("ErrorMessage")]
    public string? ErrorMessage { get; set; }

    /// <summary>
    /// المستخدم الذي أنشأ النسخة
    /// </summary>
    [ForeignKey("CreatedByUserId")]
    public virtual User? CreatedByUser { get; set; }

    /// <summary>
    /// حجم الملف بصيغة قابلة للقراءة
    /// </summary>
    [NotMapped]
    public string FileSizeFormatted
    {
        get
        {
            string[] sizes = { "B", "KB", "MB", "GB", "TB" };
            double len = FileSize;
            int order = 0;
            while (len >= 1024 && order < sizes.Length - 1)
            {
                order++;
                len = len / 1024;
            }
            return $"{len:0.##} {sizes[order]}";
        }
    }

    /// <summary>
    /// عمر النسخة الاحتياطية بالأيام
    /// </summary>
    [NotMapped]
    public int AgeInDays => (DateTime.Now - BackupDate).Days;
}
