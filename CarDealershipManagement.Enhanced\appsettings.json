{"ConnectionStrings": {"DefaultConnection": "Data Source=CarDealership.db;Cache=Shared"}, "Logging": {"LogLevel": {"Default": "Information", "Microsoft": "Warning", "Microsoft.Hosting.Lifetime": "Information", "Microsoft.EntityFrameworkCore": "Warning"}}, "Serilog": {"MinimumLevel": {"Default": "Information", "Override": {"Microsoft": "Warning", "System": "Warning"}}, "WriteTo": [{"Name": "<PERSON><PERSON><PERSON>"}, {"Name": "File", "Args": {"path": "Logs/app-.log", "rollingInterval": "Day", "retainedFileCountLimit": 30, "fileSizeLimitBytes": 10485760, "rollOnFileSizeLimit": true, "outputTemplate": "[{Timestamp:yyyy-MM-dd HH:mm:ss.fff zzz} {Level:u3}] {Message:lj}{NewLine}{Exception}"}}]}, "Application": {"Name": "Car Dealership Management System - Enhanced", "Version": "2.0.0", "Developer": "<PERSON><PERSON>", "Contact": {"Phone": "01285626623", "Email": "<EMAIL>"}, "Features": {"EnableAutoBackup": true, "BackupIntervalHours": 24, "MaxBackupFiles": 30, "EnableAuditLog": true, "EnableDataEncryption": true, "SessionTimeoutMinutes": 60}, "UI": {"DefaultTheme": "Modern", "Language": "ar-SA", "EnableRTL": true, "AutoSaveInterval": 300}, "Security": {"PasswordMinLength": 8, "RequireSpecialCharacters": true, "RequireNumbers": true, "RequireUppercase": true, "MaxLoginAttempts": 5, "LockoutDurationMinutes": 15}, "Reports": {"DefaultFormat": "PDF", "EnableWatermark": true, "CompanyLogo": "Resources/logo.png"}, "FileManagement": {"MaxFileSize": 10485760, "AllowedExtensions": [".jpg", ".jpeg", ".png", ".pdf", ".doc", ".docx", ".xls", ".xlsx"], "StoragePath": "Files", "EnableCompression": true, "EnableThumbnails": true}}, "License": {"Type": "Commercial", "CheckInterval": 24, "GracePeriodDays": 7}}