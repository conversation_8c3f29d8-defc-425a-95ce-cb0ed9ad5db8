using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using CarDealershipManagement.Enhanced.Enums;

namespace CarDealershipManagement.Enhanced.Models;

/// <summary>
/// نموذج البيع
/// </summary>
[Table("Sales")]
public class Sale : BaseEntity
{
    /// <summary>
    /// رقم البيع
    /// </summary>
    [Required(ErrorMessage = "رقم البيع مطلوب")]
    [StringLength(50, ErrorMessage = "رقم البيع يجب أن يكون أقل من 50 حرف")]
    [Column("SaleNumber")]
    public string SaleNumber { get; set; } = string.Empty;

    /// <summary>
    /// معرف السيارة
    /// </summary>
    [Required(ErrorMessage = "السيارة مطلوبة")]
    [Column("CarId")]
    public int CarId { get; set; }

    /// <summary>
    /// معرف العميل
    /// </summary>
    [Required(ErrorMessage = "العميل مطلوب")]
    [Column("CustomerId")]
    public int CustomerId { get; set; }

    /// <summary>
    /// معرف مندوب المبيعات
    /// </summary>
    [Required(ErrorMessage = "مندوب المبيعات مطلوب")]
    [Column("SalesAgentId")]
    public int SalesAgentId { get; set; }

    /// <summary>
    /// تاريخ البيع
    /// </summary>
    [Required(ErrorMessage = "تاريخ البيع مطلوب")]
    [Column("SaleDate")]
    public DateTime SaleDate { get; set; } = DateTime.Now;

    /// <summary>
    /// سعر البيع
    /// </summary>
    [Required(ErrorMessage = "سعر البيع مطلوب")]
    [Range(0, double.MaxValue, ErrorMessage = "سعر البيع يجب أن يكون أكبر من صفر")]
    [Column("SalePrice", TypeName = "decimal(18,2)")]
    public decimal SalePrice { get; set; }

    /// <summary>
    /// الخصم
    /// </summary>
    [Range(0, double.MaxValue, ErrorMessage = "الخصم يجب أن يكون أكبر من أو يساوي صفر")]
    [Column("Discount", TypeName = "decimal(18,2)")]
    public decimal Discount { get; set; } = 0;

    /// <summary>
    /// الضرائب
    /// </summary>
    [Range(0, double.MaxValue, ErrorMessage = "الضرائب يجب أن تكون أكبر من أو تساوي صفر")]
    [Column("Tax", TypeName = "decimal(18,2)")]
    public decimal Tax { get; set; } = 0;

    /// <summary>
    /// رسوم إضافية
    /// </summary>
    [Range(0, double.MaxValue, ErrorMessage = "الرسوم الإضافية يجب أن تكون أكبر من أو تساوي صفر")]
    [Column("AdditionalFees", TypeName = "decimal(18,2)")]
    public decimal AdditionalFees { get; set; } = 0;

    /// <summary>
    /// المبلغ الإجمالي
    /// </summary>
    [Required]
    [Column("TotalAmount", TypeName = "decimal(18,2)")]
    public decimal TotalAmount { get; set; }

    /// <summary>
    /// المبلغ المدفوع
    /// </summary>
    [Range(0, double.MaxValue, ErrorMessage = "المبلغ المدفوع يجب أن يكون أكبر من أو يساوي صفر")]
    [Column("PaidAmount", TypeName = "decimal(18,2)")]
    public decimal PaidAmount { get; set; } = 0;

    /// <summary>
    /// المبلغ المتبقي
    /// </summary>
    [Column("RemainingAmount", TypeName = "decimal(18,2)")]
    public decimal RemainingAmount { get; set; } = 0;

    /// <summary>
    /// نوع الدفع
    /// </summary>
    [Required]
    [Column("PaymentType")]
    public PaymentType PaymentType { get; set; } = PaymentType.Cash;

    /// <summary>
    /// هل البيع بالتقسيط
    /// </summary>
    [Column("IsInstallment")]
    public bool IsInstallment { get; set; } = false;

    /// <summary>
    /// عدد الأقساط
    /// </summary>
    [Range(0, 120, ErrorMessage = "عدد الأقساط يجب أن يكون بين 0 و 120")]
    [Column("InstallmentCount")]
    public int InstallmentCount { get; set; } = 0;

    /// <summary>
    /// قيمة القسط الشهري
    /// </summary>
    [Range(0, double.MaxValue, ErrorMessage = "قيمة القسط يجب أن تكون أكبر من أو تساوي صفر")]
    [Column("MonthlyInstallment", TypeName = "decimal(18,2)")]
    public decimal MonthlyInstallment { get; set; } = 0;

    /// <summary>
    /// معدل الفائدة السنوي
    /// </summary>
    [Range(0, 100, ErrorMessage = "معدل الفائدة يجب أن يكون بين 0 و 100")]
    [Column("InterestRate", TypeName = "decimal(5,2)")]
    public decimal InterestRate { get; set; } = 0;

    /// <summary>
    /// تاريخ أول قسط
    /// </summary>
    [Column("FirstInstallmentDate")]
    public DateTime? FirstInstallmentDate { get; set; }

    /// <summary>
    /// حالة البيع
    /// </summary>
    [Required]
    [Column("Status")]
    public SaleStatus Status { get; set; } = SaleStatus.Draft;

    /// <summary>
    /// تاريخ التأكيد
    /// </summary>
    [Column("ConfirmationDate")]
    public DateTime? ConfirmationDate { get; set; }

    /// <summary>
    /// معرف المستخدم الذي أكد البيع
    /// </summary>
    [Column("ConfirmedBy")]
    public int? ConfirmedBy { get; set; }

    /// <summary>
    /// تاريخ الإكمال
    /// </summary>
    [Column("CompletionDate")]
    public DateTime? CompletionDate { get; set; }

    /// <summary>
    /// تاريخ الإلغاء
    /// </summary>
    [Column("CancellationDate")]
    public DateTime? CancellationDate { get; set; }

    /// <summary>
    /// سبب الإلغاء
    /// </summary>
    [StringLength(500)]
    [Column("CancellationReason")]
    public string? CancellationReason { get; set; }

    /// <summary>
    /// شروط البيع
    /// </summary>
    [StringLength(2000)]
    [Column("SaleTerms")]
    public string? SaleTerms { get; set; }

    /// <summary>
    /// ملاحظات البيع
    /// </summary>
    [StringLength(2000)]
    [Column("SaleNotes")]
    public string? SaleNotes { get; set; }

    /// <summary>
    /// رقم العقد
    /// </summary>
    [StringLength(100)]
    [Column("ContractNumber")]
    public string? ContractNumber { get; set; }

    /// <summary>
    /// تاريخ تسليم السيارة
    /// </summary>
    [Column("DeliveryDate")]
    public DateTime? DeliveryDate { get; set; }

    /// <summary>
    /// مكان التسليم
    /// </summary>
    [StringLength(500)]
    [Column("DeliveryLocation")]
    public string? DeliveryLocation { get; set; }

    /// <summary>
    /// ضمان إضافي (بالشهور)
    /// </summary>
    [Range(0, 120, ErrorMessage = "فترة الضمان يجب أن تكون بين 0 و 120 شهر")]
    [Column("WarrantyMonths")]
    public int WarrantyMonths { get; set; } = 0;

    /// <summary>
    /// خدمات إضافية
    /// </summary>
    [StringLength(1000)]
    [Column("AdditionalServices")]
    public string? AdditionalServices { get; set; }

    // Navigation Properties

    /// <summary>
    /// السيارة المباعة
    /// </summary>
    [ForeignKey("CarId")]
    public virtual Car Car { get; set; } = null!;

    /// <summary>
    /// العميل
    /// </summary>
    [ForeignKey("CustomerId")]
    public virtual Customer Customer { get; set; } = null!;

    /// <summary>
    /// مندوب المبيعات
    /// </summary>
    [ForeignKey("SalesAgentId")]
    public virtual User SalesAgent { get; set; } = null!;

    /// <summary>
    /// المستخدم الذي أكد البيع
    /// </summary>
    [ForeignKey("ConfirmedBy")]
    public virtual User? ConfirmedByUser { get; set; }

    /// <summary>
    /// الأقساط المرتبطة بالبيع
    /// </summary>
    public virtual ICollection<InstallmentPayment> InstallmentPayments { get; set; } = new List<InstallmentPayment>();

    /// <summary>
    /// مستندات البيع
    /// </summary>
    public virtual ICollection<SaleDocument> SaleDocuments { get; set; } = new List<SaleDocument>();

    // Computed Properties

    /// <summary>
    /// الربح من البيع
    /// </summary>
    [NotMapped]
    public decimal Profit => SalePrice - (Car?.PurchasePrice ?? 0);

    /// <summary>
    /// نسبة الربح
    /// </summary>
    [NotMapped]
    public decimal ProfitMargin => Car?.PurchasePrice > 0 ? (Profit / Car.PurchasePrice) * 100 : 0;

    /// <summary>
    /// هل البيع مكتمل الدفع
    /// </summary>
    [NotMapped]
    public bool IsFullyPaid => RemainingAmount <= 0;

    /// <summary>
    /// نسبة الدفع
    /// </summary>
    [NotMapped]
    public decimal PaymentPercentage => TotalAmount > 0 ? (PaidAmount / TotalAmount) * 100 : 0;

    /// <summary>
    /// عدد الأقساط المدفوعة
    /// </summary>
    [NotMapped]
    public int PaidInstallments => InstallmentPayments?.Count(i => i.Status == InstallmentStatus.Paid) ?? 0;

    /// <summary>
    /// عدد الأقساط المتأخرة
    /// </summary>
    [NotMapped]
    public int OverdueInstallments => InstallmentPayments?.Count(i => i.Status == InstallmentStatus.Overdue) ?? 0;

    /// <summary>
    /// القسط التالي المستحق
    /// </summary>
    [NotMapped]
    public InstallmentPayment? NextDueInstallment => InstallmentPayments?
        .Where(i => i.Status == InstallmentStatus.Pending)
        .OrderBy(i => i.DueDate)
        .FirstOrDefault();

    /// <summary>
    /// عدد أيام منذ البيع
    /// </summary>
    [NotMapped]
    public int DaysSinceSale => (DateTime.Now - SaleDate).Days;

    /// <summary>
    /// هل البيع نشط
    /// </summary>
    [NotMapped]
    public bool IsActive => Status == SaleStatus.Confirmed || Status == SaleStatus.Completed;
}
