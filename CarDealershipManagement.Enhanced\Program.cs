using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Hosting;
using Microsoft.Extensions.Logging;
using Microsoft.EntityFrameworkCore;
using Serilog;
using CarDealershipManagement.Enhanced.Configuration;
using CarDealershipManagement.Enhanced.Data;
using CarDealershipManagement.Enhanced.Forms;
using System.Globalization;

namespace CarDealershipManagement.Enhanced;

/// <summary>
/// نقطة دخول التطبيق الرئيسية
/// </summary>
internal static class Program
{
    private static IServiceProvider? _serviceProvider;
    private static ILogger<object>? _logger;

    /// <summary>
    /// نقطة الدخول الرئيسية للتطبيق
    /// </summary>
    [STAThread]
    static async Task Main()
    {
        try
        {
            // تكوين الثقافة العربية
            ConfigureArabicCulture();

            // تكوين التطبيق
            ConfigureApplication();

            // بناء الخدمات
            await BuildServicesAsync();

            // تشغيل التطبيق
            await RunApplicationAsync();
        }
        catch (Exception ex)
        {
            // تسجيل الخطأ
            if (_logger != null)
            {
                _logger.LogCritical(ex, "خطأ فادح في بدء التطبيق");
            }
            else
            {
                MessageBox.Show($"خطأ في بدء التطبيق:\n{ex.Message}", "خطأ فادح", 
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }
        finally
        {
            // تنظيف الموارد
            await DisposeServicesAsync();
        }
    }

    /// <summary>
    /// تكوين الثقافة العربية
    /// </summary>
    private static void ConfigureArabicCulture()
    {
        var arabicCulture = new CultureInfo("ar-SA");
        arabicCulture.DateTimeFormat.Calendar = new GregorianCalendar();
        
        Thread.CurrentThread.CurrentCulture = arabicCulture;
        Thread.CurrentThread.CurrentUICulture = arabicCulture;
        
        CultureInfo.DefaultThreadCurrentCulture = arabicCulture;
        CultureInfo.DefaultThreadCurrentUICulture = arabicCulture;
    }

    /// <summary>
    /// تكوين التطبيق
    /// </summary>
    private static void ConfigureApplication()
    {
        Application.EnableVisualStyles();
        Application.SetCompatibleTextRenderingDefault(false);
        Application.SetHighDpiMode(HighDpiMode.SystemAware);
    }

    /// <summary>
    /// بناء خدمات التطبيق
    /// </summary>
    private static async Task BuildServicesAsync()
    {
        // بناء التكوين
        var configuration = BuildConfiguration();

        // تكوين Serilog
        Log.Logger = new LoggerConfiguration()
            .ReadFrom.Configuration(configuration)
            .CreateLogger();

        // بناء مجموعة الخدمات
        var services = new ServiceCollection();

        // إضافة التكوين
        services.AddSingleton<IConfiguration>(configuration);

        // تكوين الخدمات
        services.ConfigureServices(configuration);
        services.ConfigureAdditionalServices(configuration);
        services.ConfigureOptions(configuration);

        // إضافة التسجيل
        services.AddLogging(builder => builder.AddSerilog());

        // بناء مقدم الخدمات
        _serviceProvider = services.BuildServiceProvider();

        // الحصول على المسجل
        _logger = _serviceProvider.GetRequiredService<ILogger<object>>();

        // تهيئة قاعدة البيانات
        await InitializeDatabaseAsync();

        _logger.LogInformation("تم بناء خدمات التطبيق بنجاح");
    }

    /// <summary>
    /// بناء التكوين
    /// </summary>
    private static IConfiguration BuildConfiguration()
    {
        var builder = new ConfigurationBuilder()
            .SetBasePath(Directory.GetCurrentDirectory())
            .AddJsonFile("appsettings.json", optional: false, reloadOnChange: true)
            .AddJsonFile("appsettings.Development.json", optional: true, reloadOnChange: true)
            .AddEnvironmentVariables();

        return builder.Build();
    }

    /// <summary>
    /// تهيئة قاعدة البيانات
    /// </summary>
    private static async Task InitializeDatabaseAsync()
    {
        if (_serviceProvider == null) return;

        using var scope = _serviceProvider.CreateScope();
        var context = scope.ServiceProvider.GetRequiredService<CarDealershipDbContext>();

        try
        {
            // التحقق من الاتصال بقاعدة البيانات
            await context.Database.CanConnectAsync();
            
            // تطبيق الترحيلات إذا لزم الأمر
            var pendingMigrations = await context.Database.GetPendingMigrationsAsync();
            if (pendingMigrations.Any())
            {
                _logger?.LogInformation("تطبيق ترحيلات قاعدة البيانات...");
                await context.Database.MigrateAsync();
            }

            _logger?.LogInformation("تم تهيئة قاعدة البيانات بنجاح");
        }
        catch (Exception ex)
        {
            _logger?.LogError(ex, "خطأ في تهيئة قاعدة البيانات");
            throw;
        }
    }

    /// <summary>
    /// تشغيل التطبيق
    /// </summary>
    private static async Task RunApplicationAsync()
    {
        if (_serviceProvider == null)
        {
            throw new InvalidOperationException("مقدم الخدمات غير مهيأ");
        }

        _logger?.LogInformation("بدء تشغيل التطبيق...");

        // إنشاء النموذج الرئيسي
        using var scope = _serviceProvider.CreateScope();
        var mainForm = new MainForm(scope.ServiceProvider);

        // تشغيل التطبيق
        Application.Run(mainForm);

        _logger?.LogInformation("تم إنهاء التطبيق");
    }

    /// <summary>
    /// تنظيف الموارد
    /// </summary>
    private static async Task DisposeServicesAsync()
    {
        if (_serviceProvider is IAsyncDisposable asyncDisposable)
        {
            await asyncDisposable.DisposeAsync();
        }
        else if (_serviceProvider is IDisposable disposable)
        {
            disposable.Dispose();
        }

        Log.CloseAndFlush();
    }

    /// <summary>
    /// الحصول على مقدم الخدمات
    /// </summary>
    public static IServiceProvider? ServiceProvider => _serviceProvider;
}
