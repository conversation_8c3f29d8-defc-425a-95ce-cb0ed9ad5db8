using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using CarDealershipManagement.Enhanced.Enums;

namespace CarDealershipManagement.Enhanced.Models;

/// <summary>
/// نموذج السيارة
/// </summary>
[Table("Cars")]
public class Car : BaseEntity
{
    /// <summary>
    /// رقم الشاسيه (مطلوب وفريد)
    /// </summary>
    [Required(ErrorMessage = "رقم الشاسيه مطلوب")]
    [StringLength(50, ErrorMessage = "رقم الشاسيه يجب أن يكون أقل من 50 حرف")]
    [Column("ChassisNumber")]
    public string ChassisNumber { get; set; } = string.Empty;

    /// <summary>
    /// الماركة
    /// </summary>
    [Required(ErrorMessage = "الماركة مطلوبة")]
    [StringLength(100, ErrorMessage = "الماركة يجب أن تكون أقل من 100 حرف")]
    [Column("Brand")]
    public string Brand { get; set; } = string.Empty;

    /// <summary>
    /// الموديل
    /// </summary>
    [Required(ErrorMessage = "الموديل مطلوب")]
    [StringLength(100, ErrorMessage = "الموديل يجب أن يكون أقل من 100 حرف")]
    [Column("Model")]
    public string Model { get; set; } = string.Empty;

    /// <summary>
    /// سنة الصنع
    /// </summary>
    [Required(ErrorMessage = "سنة الصنع مطلوبة")]
    [Range(1900, 2100, ErrorMessage = "سنة الصنع يجب أن تكون بين 1900 و 2100")]
    [Column("Year")]
    public int Year { get; set; }

    /// <summary>
    /// اللون
    /// </summary>
    [Required(ErrorMessage = "اللون مطلوب")]
    [StringLength(50, ErrorMessage = "اللون يجب أن يكون أقل من 50 حرف")]
    [Column("Color")]
    public string Color { get; set; } = string.Empty;

    /// <summary>
    /// نوع الوقود
    /// </summary>
    [StringLength(50)]
    [Column("FuelType")]
    public string? FuelType { get; set; }

    /// <summary>
    /// نوع ناقل الحركة
    /// </summary>
    [StringLength(50)]
    [Column("Transmission")]
    public string? Transmission { get; set; }

    /// <summary>
    /// حجم المحرك
    /// </summary>
    [Column("EngineSize")]
    public decimal? EngineSize { get; set; }

    /// <summary>
    /// عدد الكيلومترات
    /// </summary>
    [Range(0, int.MaxValue, ErrorMessage = "عدد الكيلومترات يجب أن يكون أكبر من أو يساوي صفر")]
    [Column("Mileage")]
    public int Mileage { get; set; } = 0;

    /// <summary>
    /// سعر الشراء
    /// </summary>
    [Required(ErrorMessage = "سعر الشراء مطلوب")]
    [Range(0, double.MaxValue, ErrorMessage = "سعر الشراء يجب أن يكون أكبر من صفر")]
    [Column("PurchasePrice", TypeName = "decimal(18,2)")]
    public decimal PurchasePrice { get; set; }

    /// <summary>
    /// سعر البيع
    /// </summary>
    [Required(ErrorMessage = "سعر البيع مطلوب")]
    [Range(0, double.MaxValue, ErrorMessage = "سعر البيع يجب أن يكون أكبر من صفر")]
    [Column("SalePrice", TypeName = "decimal(18,2)")]
    public decimal SalePrice { get; set; }

    /// <summary>
    /// حالة السيارة
    /// </summary>
    [Required]
    [Column("Status")]
    public CarStatus Status { get; set; } = CarStatus.Available;

    /// <summary>
    /// تاريخ الشراء
    /// </summary>
    [Column("PurchaseDate")]
    public DateTime? PurchaseDate { get; set; }

    /// <summary>
    /// تاريخ البيع
    /// </summary>
    [Column("SaleDate")]
    public DateTime? SaleDate { get; set; }

    /// <summary>
    /// معرف المورد
    /// </summary>
    [Column("SupplierId")]
    public int? SupplierId { get; set; }

    /// <summary>
    /// معرف العميل (في حالة البيع)
    /// </summary>
    [Column("CustomerId")]
    public int? CustomerId { get; set; }

    /// <summary>
    /// الوصف
    /// </summary>
    [StringLength(1000)]
    [Column("Description")]
    public string? Description { get; set; }

    /// <summary>
    /// الميزات الإضافية
    /// </summary>
    [StringLength(2000)]
    [Column("Features")]
    public string? Features { get; set; }

    /// <summary>
    /// موقع السيارة في المعرض
    /// </summary>
    [StringLength(100)]
    [Column("Location")]
    public string? Location { get; set; }

    /// <summary>
    /// رقم اللوحة
    /// </summary>
    [StringLength(20)]
    [Column("PlateNumber")]
    public string? PlateNumber { get; set; }

    /// <summary>
    /// تاريخ انتهاء التأمين
    /// </summary>
    [Column("InsuranceExpiry")]
    public DateTime? InsuranceExpiry { get; set; }

    /// <summary>
    /// تاريخ انتهاء الرخصة
    /// </summary>
    [Column("LicenseExpiry")]
    public DateTime? LicenseExpiry { get; set; }

    /// <summary>
    /// هل السيارة مستعملة
    /// </summary>
    [Column("IsUsed")]
    public bool IsUsed { get; set; } = true;

    /// <summary>
    /// عدد المالكين السابقين
    /// </summary>
    [Column("PreviousOwners")]
    public int PreviousOwners { get; set; } = 0;

    /// <summary>
    /// حالة الحادث
    /// </summary>
    [StringLength(500)]
    [Column("AccidentHistory")]
    public string? AccidentHistory { get; set; }

    /// <summary>
    /// تاريخ آخر صيانة
    /// </summary>
    [Column("LastMaintenanceDate")]
    public DateTime? LastMaintenanceDate { get; set; }

    /// <summary>
    /// تاريخ الصيانة القادمة
    /// </summary>
    [Column("NextMaintenanceDate")]
    public DateTime? NextMaintenanceDate { get; set; }

    // Navigation Properties

    /// <summary>
    /// المورد
    /// </summary>
    [ForeignKey("SupplierId")]
    public virtual Supplier? Supplier { get; set; }

    /// <summary>
    /// العميل
    /// </summary>
    [ForeignKey("CustomerId")]
    public virtual Customer? Customer { get; set; }

    /// <summary>
    /// صور السيارة
    /// </summary>
    public virtual ICollection<CarImage> CarImages { get; set; } = new List<CarImage>();

    /// <summary>
    /// مستندات السيارة
    /// </summary>
    public virtual ICollection<CarDocument> CarDocuments { get; set; } = new List<CarDocument>();

    /// <summary>
    /// المبيعات المرتبطة بالسيارة
    /// </summary>
    public virtual ICollection<Sale> Sales { get; set; } = new List<Sale>();

    /// <summary>
    /// سجل الصيانة
    /// </summary>
    public virtual ICollection<MaintenanceRecord> MaintenanceRecords { get; set; } = new List<MaintenanceRecord>();

    // Computed Properties

    /// <summary>
    /// الربح المتوقع
    /// </summary>
    [NotMapped]
    public decimal ExpectedProfit => SalePrice - PurchasePrice;

    /// <summary>
    /// نسبة الربح
    /// </summary>
    [NotMapped]
    public decimal ProfitMargin => PurchasePrice > 0 ? (ExpectedProfit / PurchasePrice) * 100 : 0;

    /// <summary>
    /// عمر السيارة بالسنوات
    /// </summary>
    [NotMapped]
    public int Age => DateTime.Now.Year - Year;

    /// <summary>
    /// هل السيارة متاحة للبيع
    /// </summary>
    [NotMapped]
    public bool IsAvailableForSale => Status == CarStatus.Available;

    /// <summary>
    /// الاسم الكامل للسيارة
    /// </summary>
    [NotMapped]
    public string FullName => $"{Brand} {Model} {Year}";

    /// <summary>
    /// عدد الأيام في المعرض
    /// </summary>
    [NotMapped]
    public int DaysInShowroom => PurchaseDate.HasValue ? (DateTime.Now - PurchaseDate.Value).Days : 0;
}
