using CarDealershipManagement.Enhanced.Models;
using CarDealershipManagement.Enhanced.Enums;

namespace CarDealershipManagement.Enhanced.Services.Interfaces;

/// <summary>
/// واجهة خدمة الأقساط
/// </summary>
public interface IInstallmentService : IBaseService<InstallmentPayment>
{
    /// <summary>
    /// الحصول على الأقساط حسب البيع
    /// </summary>
    /// <param name="saleId">معرف البيع</param>
    /// <returns>قائمة الأقساط</returns>
    Task<IEnumerable<InstallmentPayment>> GetBySaleAsync(int saleId);

    /// <summary>
    /// الحصول على الأقساط حسب العميل
    /// </summary>
    /// <param name="customerId">معرف العميل</param>
    /// <returns>قائمة الأقساط</returns>
    Task<IEnumerable<InstallmentPayment>> GetByCustomerAsync(int customerId);

    /// <summary>
    /// الحصول على الأقساط حسب الحالة
    /// </summary>
    /// <param name="status">الحالة</param>
    /// <returns>قائمة الأقساط</returns>
    Task<IEnumerable<InstallmentPayment>> GetByStatusAsync(InstallmentStatus status);

    /// <summary>
    /// الحصول على الأقساط المستحقة
    /// </summary>
    /// <param name="dueDate">تاريخ الاستحقاق (اختياري)</param>
    /// <returns>قائمة الأقساط المستحقة</returns>
    Task<IEnumerable<InstallmentPayment>> GetDueInstallmentsAsync(DateTime? dueDate = null);

    /// <summary>
    /// الحصول على الأقساط المتأخرة
    /// </summary>
    /// <returns>قائمة الأقساط المتأخرة</returns>
    Task<IEnumerable<InstallmentPayment>> GetOverdueInstallmentsAsync();

    /// <summary>
    /// الحصول على الأقساط المدفوعة
    /// </summary>
    /// <param name="startDate">تاريخ البداية</param>
    /// <param name="endDate">تاريخ النهاية</param>
    /// <returns>قائمة الأقساط المدفوعة</returns>
    Task<IEnumerable<InstallmentPayment>> GetPaidInstallmentsAsync(DateTime? startDate = null, DateTime? endDate = null);

    /// <summary>
    /// إنشاء جدول أقساط للبيع
    /// </summary>
    /// <param name="saleId">معرف البيع</param>
    /// <param name="numberOfInstallments">عدد الأقساط</param>
    /// <param name="firstInstallmentDate">تاريخ أول قسط</param>
    /// <param name="userId">معرف المستخدم</param>
    /// <returns>قائمة الأقساط المنشأة</returns>
    Task<IEnumerable<InstallmentPayment>> CreateInstallmentScheduleAsync(
        int saleId,
        int numberOfInstallments,
        DateTime firstInstallmentDate,
        int? userId = null);

    /// <summary>
    /// تسجيل دفعة قسط
    /// </summary>
    /// <param name="installmentId">معرف القسط</param>
    /// <param name="paidAmount">المبلغ المدفوع</param>
    /// <param name="paymentDate">تاريخ الدفع</param>
    /// <param name="paymentType">نوع الدفع</param>
    /// <param name="notes">ملاحظات</param>
    /// <param name="userId">معرف المستخدم</param>
    /// <returns>true إذا تم تسجيل الدفعة بنجاح</returns>
    Task<bool> RecordPaymentAsync(
        int installmentId,
        decimal paidAmount,
        DateTime paymentDate,
        PaymentType paymentType,
        string? notes = null,
        int? userId = null);

    /// <summary>
    /// تسجيل دفعة جزئية للقسط
    /// </summary>
    /// <param name="installmentId">معرف القسط</param>
    /// <param name="partialAmount">المبلغ الجزئي</param>
    /// <param name="paymentDate">تاريخ الدفع</param>
    /// <param name="paymentType">نوع الدفع</param>
    /// <param name="notes">ملاحظات</param>
    /// <param name="userId">معرف المستخدم</param>
    /// <returns>true إذا تم تسجيل الدفعة بنجاح</returns>
    Task<bool> RecordPartialPaymentAsync(
        int installmentId,
        decimal partialAmount,
        DateTime paymentDate,
        PaymentType paymentType,
        string? notes = null,
        int? userId = null);

    /// <summary>
    /// حساب الغرامة للقسط المتأخر
    /// </summary>
    /// <param name="installmentId">معرف القسط</param>
    /// <returns>مبلغ الغرامة</returns>
    Task<decimal> CalculateLateFeeAsync(int installmentId);

    /// <summary>
    /// تطبيق الغرامة على القسط المتأخر
    /// </summary>
    /// <param name="installmentId">معرف القسط</param>
    /// <param name="userId">معرف المستخدم</param>
    /// <returns>true إذا تم تطبيق الغرامة بنجاح</returns>
    Task<bool> ApplyLateFeeAsync(int installmentId, int? userId = null);

    /// <summary>
    /// تأجيل القسط
    /// </summary>
    /// <param name="installmentId">معرف القسط</param>
    /// <param name="newDueDate">تاريخ الاستحقاق الجديد</param>
    /// <param name="reason">سبب التأجيل</param>
    /// <param name="userId">معرف المستخدم</param>
    /// <returns>true إذا تم التأجيل بنجاح</returns>
    Task<bool> PostponeInstallmentAsync(int installmentId, DateTime newDueDate, string reason, int? userId = null);

    /// <summary>
    /// إلغاء القسط
    /// </summary>
    /// <param name="installmentId">معرف القسط</param>
    /// <param name="reason">سبب الإلغاء</param>
    /// <param name="userId">معرف المستخدم</param>
    /// <returns>true إذا تم الإلغاء بنجاح</returns>
    Task<bool> CancelInstallmentAsync(int installmentId, string reason, int? userId = null);

    /// <summary>
    /// إعادة جدولة الأقساط المتبقية
    /// </summary>
    /// <param name="saleId">معرف البيع</param>
    /// <param name="newSchedule">الجدولة الجديدة</param>
    /// <param name="userId">معرف المستخدم</param>
    /// <returns>قائمة الأقساط المعاد جدولتها</returns>
    Task<IEnumerable<InstallmentPayment>> RescheduleRemainingInstallmentsAsync(
        int saleId,
        List<InstallmentScheduleItem> newSchedule,
        int? userId = null);

    /// <summary>
    /// حساب إجمالي المبلغ المستحق للعميل
    /// </summary>
    /// <param name="customerId">معرف العميل</param>
    /// <returns>إجمالي المبلغ المستحق</returns>
    Task<decimal> CalculateCustomerTotalDueAsync(int customerId);

    /// <summary>
    /// حساب إجمالي المبلغ المدفوع للعميل
    /// </summary>
    /// <param name="customerId">معرف العميل</param>
    /// <returns>إجمالي المبلغ المدفوع</returns>
    Task<decimal> CalculateCustomerTotalPaidAsync(int customerId);

    /// <summary>
    /// الحصول على تقرير الأقساط للعميل
    /// </summary>
    /// <param name="customerId">معرف العميل</param>
    /// <returns>تقرير الأقساط</returns>
    Task<CustomerInstallmentReport> GetCustomerInstallmentReportAsync(int customerId);

    /// <summary>
    /// الحصول على إحصائيات الأقساط
    /// </summary>
    /// <param name="startDate">تاريخ البداية</param>
    /// <param name="endDate">تاريخ النهاية</param>
    /// <returns>إحصائيات الأقساط</returns>
    Task<InstallmentStatistics> GetInstallmentStatisticsAsync(DateTime? startDate = null, DateTime? endDate = null);

    /// <summary>
    /// إرسال تذكير بالأقساط المستحقة
    /// </summary>
    /// <param name="daysBeforeDue">عدد الأيام قبل الاستحقاق</param>
    /// <param name="userId">معرف المستخدم</param>
    /// <returns>عدد التذكيرات المرسلة</returns>
    Task<int> SendInstallmentRemindersAsync(int daysBeforeDue = 3, int? userId = null);

    /// <summary>
    /// البحث في الأقساط بمعايير متقدمة
    /// </summary>
    /// <param name="customerId">معرف العميل</param>
    /// <param name="saleId">معرف البيع</param>
    /// <param name="status">الحالة</param>
    /// <param name="startDate">تاريخ البداية</param>
    /// <param name="endDate">تاريخ النهاية</param>
    /// <param name="minAmount">أقل مبلغ</param>
    /// <param name="maxAmount">أعلى مبلغ</param>
    /// <returns>قائمة الأقساط المطابقة</returns>
    Task<IEnumerable<InstallmentPayment>> SearchInstallmentsAsync(
        int? customerId = null,
        int? saleId = null,
        InstallmentStatus? status = null,
        DateTime? startDate = null,
        DateTime? endDate = null,
        decimal? minAmount = null,
        decimal? maxAmount = null);

    /// <summary>
    /// تحديث حالة الأقساط المتأخرة تلقائياً
    /// </summary>
    /// <param name="userId">معرف المستخدم</param>
    /// <returns>عدد الأقساط المحدثة</returns>
    Task<int> UpdateOverdueInstallmentsAsync(int? userId = null);
}

/// <summary>
/// عنصر جدولة القسط
/// </summary>
public class InstallmentScheduleItem
{
    /// <summary>
    /// رقم القسط
    /// </summary>
    public int InstallmentNumber { get; set; }

    /// <summary>
    /// تاريخ الاستحقاق
    /// </summary>
    public DateTime DueDate { get; set; }

    /// <summary>
    /// مبلغ القسط
    /// </summary>
    public decimal Amount { get; set; }
}

/// <summary>
/// تقرير أقساط العميل
/// </summary>
public class CustomerInstallmentReport
{
    /// <summary>
    /// معرف العميل
    /// </summary>
    public int CustomerId { get; set; }

    /// <summary>
    /// اسم العميل
    /// </summary>
    public string CustomerName { get; set; } = string.Empty;

    /// <summary>
    /// إجمالي المبلغ المستحق
    /// </summary>
    public decimal TotalDue { get; set; }

    /// <summary>
    /// إجمالي المبلغ المدفوع
    /// </summary>
    public decimal TotalPaid { get; set; }

    /// <summary>
    /// الرصيد المتبقي
    /// </summary>
    public decimal RemainingBalance { get; set; }

    /// <summary>
    /// عدد الأقساط الإجمالي
    /// </summary>
    public int TotalInstallments { get; set; }

    /// <summary>
    /// عدد الأقساط المدفوعة
    /// </summary>
    public int PaidInstallments { get; set; }

    /// <summary>
    /// عدد الأقساط المتأخرة
    /// </summary>
    public int OverdueInstallments { get; set; }

    /// <summary>
    /// إجمالي الغرامات
    /// </summary>
    public decimal TotalLateFees { get; set; }

    /// <summary>
    /// تاريخ آخر دفعة
    /// </summary>
    public DateTime? LastPaymentDate { get; set; }

    /// <summary>
    /// تاريخ القسط القادم
    /// </summary>
    public DateTime? NextDueDate { get; set; }
}

/// <summary>
/// إحصائيات الأقساط
/// </summary>
public class InstallmentStatistics
{
    /// <summary>
    /// العدد الإجمالي للأقساط
    /// </summary>
    public int TotalInstallments { get; set; }

    /// <summary>
    /// عدد الأقساط المدفوعة
    /// </summary>
    public int PaidInstallments { get; set; }

    /// <summary>
    /// عدد الأقساط المعلقة
    /// </summary>
    public int PendingInstallments { get; set; }

    /// <summary>
    /// عدد الأقساط المتأخرة
    /// </summary>
    public int OverdueInstallments { get; set; }

    /// <summary>
    /// إجمالي المبلغ المستحق
    /// </summary>
    public decimal TotalDueAmount { get; set; }

    /// <summary>
    /// إجمالي المبلغ المدفوع
    /// </summary>
    public decimal TotalPaidAmount { get; set; }

    /// <summary>
    /// إجمالي الغرامات
    /// </summary>
    public decimal TotalLateFees { get; set; }

    /// <summary>
    /// متوسط مبلغ القسط
    /// </summary>
    public decimal AverageInstallmentAmount { get; set; }

    /// <summary>
    /// معدل الدفع في الوقت المحدد
    /// </summary>
    public double OnTimePaymentRate { get; set; }

    /// <summary>
    /// عدد العملاء الذين لديهم أقساط متأخرة
    /// </summary>
    public int CustomersWithOverdueInstallments { get; set; }
}
