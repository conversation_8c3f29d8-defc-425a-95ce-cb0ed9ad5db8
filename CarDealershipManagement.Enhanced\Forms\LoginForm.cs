using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;
using CarDealershipManagement.Enhanced.Services.Interfaces;
using CarDealershipManagement.Enhanced.Models;

namespace CarDealershipManagement.Enhanced.Forms;

/// <summary>
/// نموذج تسجيل الدخول
/// </summary>
public partial class LoginForm : Form
{
    private readonly IServiceProvider _serviceProvider;
    private readonly ILogger<LoginForm> _logger;
    private readonly IUserService _userService;
    private readonly ISecurityService _securityService;

    public User? CurrentUser { get; private set; }

    public LoginForm(IServiceProvider serviceProvider)
    {
        _serviceProvider = serviceProvider;
        _logger = serviceProvider.GetRequiredService<ILogger<LoginForm>>();
        _userService = serviceProvider.GetRequiredService<IUserService>();
        _securityService = serviceProvider.GetRequiredService<ISecurityService>();

        InitializeComponent();
        InitializeForm();
    }

    /// <summary>
    /// تهيئة النموذج
    /// </summary>
    private void InitializeForm()
    {
        // تعيين القيم الافتراضية
        txtUsername.Text = "admin";
        txtPassword.UseSystemPasswordChar = true;
        
        // تعيين الأحداث
        txtUsername.KeyPress += TextBox_KeyPress;
        txtPassword.KeyPress += TextBox_KeyPress;
        
        // التركيز على حقل اسم المستخدم
        txtUsername.Focus();
    }

    /// <summary>
    /// معالج الضغط على Enter
    /// </summary>
    private void TextBox_KeyPress(object sender, KeyPressEventArgs e)
    {
        if (e.KeyChar == (char)Keys.Enter)
        {
            e.Handled = true;
            if (sender == txtUsername)
            {
                txtPassword.Focus();
            }
            else if (sender == txtPassword)
            {
                btnLogin_Click(sender, EventArgs.Empty);
            }
        }
    }

    /// <summary>
    /// تسجيل الدخول
    /// </summary>
    private async void btnLogin_Click(object sender, EventArgs e)
    {
        try
        {
            // التحقق من صحة البيانات
            if (string.IsNullOrWhiteSpace(txtUsername.Text))
            {
                MessageBox.Show("يرجى إدخال اسم المستخدم", "خطأ", 
                    MessageBoxButtons.OK, MessageBoxIcon.Warning);
                txtUsername.Focus();
                return;
            }

            if (string.IsNullOrWhiteSpace(txtPassword.Text))
            {
                MessageBox.Show("يرجى إدخال كلمة المرور", "خطأ", 
                    MessageBoxButtons.OK, MessageBoxIcon.Warning);
                txtPassword.Focus();
                return;
            }

            // تعطيل الأزرار أثناء المعالجة
            SetControlsEnabled(false);
            lblStatus.Text = "جاري التحقق من بيانات الدخول...";
            lblStatus.ForeColor = Color.Blue;

            // محاولة تسجيل الدخول
            var loginResult = await _userService.AuthenticateAsync(txtUsername.Text, txtPassword.Text);

            if (loginResult.IsSuccess && loginResult.User != null)
            {
                // التحقق من حالة الحساب
                if (await _securityService.IsAccountLockedAsync(loginResult.User.Id))
                {
                    MessageBox.Show("الحساب مقفل مؤقتاً. يرجى المحاولة لاحقاً", "حساب مقفل", 
                        MessageBoxButtons.OK, MessageBoxIcon.Warning);
                    SetControlsEnabled(true);
                    return;
                }

                // تسجيل الدخول بنجاح
                CurrentUser = loginResult.User;
                lblStatus.Text = "تم تسجيل الدخول بنجاح";
                lblStatus.ForeColor = Color.Green;

                _logger.LogInformation("تم تسجيل دخول المستخدم: {Username}", txtUsername.Text);

                // إغلاق النموذج بنجاح
                DialogResult = DialogResult.OK;
                Close();
            }
            else
            {
                // فشل تسجيل الدخول
                lblStatus.Text = "اسم المستخدم أو كلمة المرور غير صحيحة";
                lblStatus.ForeColor = Color.Red;

                _logger.LogWarning("محاولة دخول فاشلة لاسم المستخدم: {Username}", txtUsername.Text);

                // مسح كلمة المرور
                txtPassword.Clear();
                txtPassword.Focus();

                SetControlsEnabled(true);
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "خطأ في تسجيل الدخول");
            
            lblStatus.Text = "خطأ في الاتصال بقاعدة البيانات";
            lblStatus.ForeColor = Color.Red;
            
            MessageBox.Show($"خطأ في تسجيل الدخول:\n{ex.Message}", "خطأ", 
                MessageBoxButtons.OK, MessageBoxIcon.Error);
            
            SetControlsEnabled(true);
        }
    }

    /// <summary>
    /// إلغاء تسجيل الدخول
    /// </summary>
    private void btnCancel_Click(object sender, EventArgs e)
    {
        DialogResult = DialogResult.Cancel;
        Close();
    }

    /// <summary>
    /// تفعيل/تعطيل العناصر
    /// </summary>
    private void SetControlsEnabled(bool enabled)
    {
        txtUsername.Enabled = enabled;
        txtPassword.Enabled = enabled;
        btnLogin.Enabled = enabled;
        btnCancel.Enabled = enabled;

        if (enabled)
        {
            lblStatus.Text = "أدخل بيانات الدخول";
            lblStatus.ForeColor = Color.Black;
        }
    }

    /// <summary>
    /// عرض معلومات حول البرنامج
    /// </summary>
    private void lblAbout_Click(object sender, EventArgs e)
    {
        var aboutMessage = @"نظام إدارة معارض السيارات - النسخة المحسنة
الإصدار: 2.0.0
تطوير: فريق التطوير المحسن
تاريخ الإصدار: " + DateTime.Now.ToString("yyyy/MM/dd");

        MessageBox.Show(aboutMessage, "حول البرنامج", 
            MessageBoxButtons.OK, MessageBoxIcon.Information);
    }

    /// <summary>
    /// إظهار/إخفاء كلمة المرور
    /// </summary>
    private void chkShowPassword_CheckedChanged(object sender, EventArgs e)
    {
        txtPassword.UseSystemPasswordChar = !chkShowPassword.Checked;
    }
}
