using CarDealershipManagement.Enhanced.Models;
using System.Linq.Expressions;

namespace CarDealershipManagement.Enhanced.Services.Interfaces;

/// <summary>
/// الواجهة الأساسية للخدمات
/// </summary>
/// <typeparam name="T">نوع الكيان</typeparam>
public interface IBaseService<T> where T : BaseEntity
{
    /// <summary>
    /// الحصول على جميع السجلات
    /// </summary>
    /// <param name="includeDeleted">تضمين السجلات المحذوفة</param>
    /// <returns>قائمة السجلات</returns>
    Task<IEnumerable<T>> GetAllAsync(bool includeDeleted = false);

    /// <summary>
    /// الحصول على سجل بالمعرف
    /// </summary>
    /// <param name="id">المعرف</param>
    /// <param name="includeDeleted">تضمين السجلات المحذوفة</param>
    /// <returns>السجل أو null</returns>
    Task<T?> GetByIdAsync(int id, bool includeDeleted = false);

    /// <summary>
    /// البحث في السجلات
    /// </summary>
    /// <param name="predicate">شرط البحث</param>
    /// <param name="includeDeleted">تضمين السجلات المحذوفة</param>
    /// <returns>قائمة السجلات المطابقة</returns>
    Task<IEnumerable<T>> FindAsync(Expression<Func<T, bool>> predicate, bool includeDeleted = false);

    /// <summary>
    /// الحصول على أول سجل مطابق
    /// </summary>
    /// <param name="predicate">شرط البحث</param>
    /// <param name="includeDeleted">تضمين السجلات المحذوفة</param>
    /// <returns>السجل أو null</returns>
    Task<T?> FirstOrDefaultAsync(Expression<Func<T, bool>> predicate, bool includeDeleted = false);

    /// <summary>
    /// إضافة سجل جديد
    /// </summary>
    /// <param name="entity">السجل الجديد</param>
    /// <param name="userId">معرف المستخدم</param>
    /// <returns>السجل المضاف</returns>
    Task<T> AddAsync(T entity, int? userId = null);

    /// <summary>
    /// إضافة عدة سجلات
    /// </summary>
    /// <param name="entities">السجلات الجديدة</param>
    /// <param name="userId">معرف المستخدم</param>
    /// <returns>السجلات المضافة</returns>
    Task<IEnumerable<T>> AddRangeAsync(IEnumerable<T> entities, int? userId = null);

    /// <summary>
    /// تحديث سجل
    /// </summary>
    /// <param name="entity">السجل المحدث</param>
    /// <param name="userId">معرف المستخدم</param>
    /// <returns>السجل المحدث</returns>
    Task<T> UpdateAsync(T entity, int? userId = null);

    /// <summary>
    /// تحديث عدة سجلات
    /// </summary>
    /// <param name="entities">السجلات المحدثة</param>
    /// <param name="userId">معرف المستخدم</param>
    /// <returns>السجلات المحدثة</returns>
    Task<IEnumerable<T>> UpdateRangeAsync(IEnumerable<T> entities, int? userId = null);

    /// <summary>
    /// حذف سجل (حذف ناعم)
    /// </summary>
    /// <param name="id">معرف السجل</param>
    /// <param name="userId">معرف المستخدم</param>
    /// <returns>true إذا تم الحذف بنجاح</returns>
    Task<bool> DeleteAsync(int id, int? userId = null);

    /// <summary>
    /// حذف سجل (حذف ناعم)
    /// </summary>
    /// <param name="entity">السجل المراد حذفه</param>
    /// <param name="userId">معرف المستخدم</param>
    /// <returns>true إذا تم الحذف بنجاح</returns>
    Task<bool> DeleteAsync(T entity, int? userId = null);

    /// <summary>
    /// حذف عدة سجلات (حذف ناعم)
    /// </summary>
    /// <param name="ids">معرفات السجلات</param>
    /// <param name="userId">معرف المستخدم</param>
    /// <returns>عدد السجلات المحذوفة</returns>
    Task<int> DeleteRangeAsync(IEnumerable<int> ids, int? userId = null);

    /// <summary>
    /// استعادة سجل محذوف
    /// </summary>
    /// <param name="id">معرف السجل</param>
    /// <param name="userId">معرف المستخدم</param>
    /// <returns>true إذا تم الاستعادة بنجاح</returns>
    Task<bool> RestoreAsync(int id, int? userId = null);

    /// <summary>
    /// حذف سجل نهائياً
    /// </summary>
    /// <param name="id">معرف السجل</param>
    /// <returns>true إذا تم الحذف بنجاح</returns>
    Task<bool> PermanentDeleteAsync(int id);

    /// <summary>
    /// التحقق من وجود سجل
    /// </summary>
    /// <param name="predicate">شرط البحث</param>
    /// <param name="includeDeleted">تضمين السجلات المحذوفة</param>
    /// <returns>true إذا كان السجل موجود</returns>
    Task<bool> ExistsAsync(Expression<Func<T, bool>> predicate, bool includeDeleted = false);

    /// <summary>
    /// عدد السجلات
    /// </summary>
    /// <param name="predicate">شرط البحث (اختياري)</param>
    /// <param name="includeDeleted">تضمين السجلات المحذوفة</param>
    /// <returns>عدد السجلات</returns>
    Task<int> CountAsync(Expression<Func<T, bool>>? predicate = null, bool includeDeleted = false);

    /// <summary>
    /// الحصول على السجلات مع الترقيم
    /// </summary>
    /// <param name="pageNumber">رقم الصفحة</param>
    /// <param name="pageSize">حجم الصفحة</param>
    /// <param name="predicate">شرط البحث (اختياري)</param>
    /// <param name="orderBy">ترتيب النتائج (اختياري)</param>
    /// <param name="includeDeleted">تضمين السجلات المحذوفة</param>
    /// <returns>نتيجة مرقمة</returns>
    Task<PagedResult<T>> GetPagedAsync(
        int pageNumber,
        int pageSize,
        Expression<Func<T, bool>>? predicate = null,
        Func<IQueryable<T>, IOrderedQueryable<T>>? orderBy = null,
        bool includeDeleted = false);

    /// <summary>
    /// حفظ التغييرات
    /// </summary>
    /// <returns>عدد السجلات المتأثرة</returns>
    Task<int> SaveChangesAsync();
}

/// <summary>
/// نتيجة مرقمة
/// </summary>
/// <typeparam name="T">نوع البيانات</typeparam>
public class PagedResult<T>
{
    /// <summary>
    /// البيانات
    /// </summary>
    public IEnumerable<T> Data { get; set; } = new List<T>();

    /// <summary>
    /// العدد الإجمالي للسجلات
    /// </summary>
    public int TotalCount { get; set; }

    /// <summary>
    /// رقم الصفحة الحالية
    /// </summary>
    public int PageNumber { get; set; }

    /// <summary>
    /// حجم الصفحة
    /// </summary>
    public int PageSize { get; set; }

    /// <summary>
    /// العدد الإجمالي للصفحات
    /// </summary>
    public int TotalPages => (int)Math.Ceiling((double)TotalCount / PageSize);

    /// <summary>
    /// هل توجد صفحة سابقة
    /// </summary>
    public bool HasPreviousPage => PageNumber > 1;

    /// <summary>
    /// هل توجد صفحة تالية
    /// </summary>
    public bool HasNextPage => PageNumber < TotalPages;

    /// <summary>
    /// رقم الصفحة السابقة
    /// </summary>
    public int? PreviousPageNumber => HasPreviousPage ? PageNumber - 1 : null;

    /// <summary>
    /// رقم الصفحة التالية
    /// </summary>
    public int? NextPageNumber => HasNextPage ? PageNumber + 1 : null;
}
