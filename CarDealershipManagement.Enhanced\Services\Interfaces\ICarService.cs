using CarDealershipManagement.Enhanced.Models;
using CarDealershipManagement.Enhanced.Enums;

namespace CarDealershipManagement.Enhanced.Services.Interfaces;

/// <summary>
/// واجهة خدمة السيارات
/// </summary>
public interface ICarService : IBaseService<Car>
{
    /// <summary>
    /// البحث في السيارات بالرقم التسلسلي
    /// </summary>
    /// <param name="chassisNumber">الرقم التسلسلي</param>
    /// <returns>السيارة أو null</returns>
    Task<Car?> GetByChassisNumberAsync(string chassisNumber);

    /// <summary>
    /// الحصول على السيارات حسب الحالة
    /// </summary>
    /// <param name="status">الحالة</param>
    /// <returns>قائمة السيارات</returns>
    Task<IEnumerable<Car>> GetByStatusAsync(CarStatus status);

    /// <summary>
    /// الحصول على السيارات المتاحة للبيع
    /// </summary>
    /// <returns>قائمة السيارات المتاحة</returns>
    Task<IEnumerable<Car>> GetAvailableCarsAsync();

    /// <summary>
    /// الحصول على السيارات المباعة
    /// </summary>
    /// <returns>قائمة السيارات المباعة</returns>
    Task<IEnumerable<Car>> GetSoldCarsAsync();

    /// <summary>
    /// الحصول على السيارات حسب المورد
    /// </summary>
    /// <param name="supplierId">معرف المورد</param>
    /// <returns>قائمة السيارات</returns>
    Task<IEnumerable<Car>> GetBySupplierAsync(int supplierId);

    /// <summary>
    /// الحصول على السيارات حسب العميل
    /// </summary>
    /// <param name="customerId">معرف العميل</param>
    /// <returns>قائمة السيارات</returns>
    Task<IEnumerable<Car>> GetByCustomerAsync(int customerId);

    /// <summary>
    /// البحث في السيارات بمعايير متقدمة
    /// </summary>
    /// <param name="brand">الماركة</param>
    /// <param name="model">الموديل</param>
    /// <param name="year">السنة</param>
    /// <param name="color">اللون</param>
    /// <param name="minPrice">أقل سعر</param>
    /// <param name="maxPrice">أعلى سعر</param>
    /// <param name="status">الحالة</param>
    /// <returns>قائمة السيارات المطابقة</returns>
    Task<IEnumerable<Car>> SearchCarsAsync(
        string? brand = null,
        string? model = null,
        int? year = null,
        string? color = null,
        decimal? minPrice = null,
        decimal? maxPrice = null,
        CarStatus? status = null);

    /// <summary>
    /// تحديث حالة السيارة
    /// </summary>
    /// <param name="carId">معرف السيارة</param>
    /// <param name="status">الحالة الجديدة</param>
    /// <param name="userId">معرف المستخدم</param>
    /// <returns>true إذا تم التحديث بنجاح</returns>
    Task<bool> UpdateCarStatusAsync(int carId, CarStatus status, int? userId = null);

    /// <summary>
    /// تحديث سعر السيارة
    /// </summary>
    /// <param name="carId">معرف السيارة</param>
    /// <param name="newPrice">السعر الجديد</param>
    /// <param name="userId">معرف المستخدم</param>
    /// <returns>true إذا تم التحديث بنجاح</returns>
    Task<bool> UpdateCarPriceAsync(int carId, decimal newPrice, int? userId = null);

    /// <summary>
    /// إضافة صورة للسيارة
    /// </summary>
    /// <param name="carId">معرف السيارة</param>
    /// <param name="image">بيانات الصورة</param>
    /// <param name="userId">معرف المستخدم</param>
    /// <returns>الصورة المضافة</returns>
    Task<CarImage> AddCarImageAsync(int carId, CarImage image, int? userId = null);

    /// <summary>
    /// إضافة مستند للسيارة
    /// </summary>
    /// <param name="carId">معرف السيارة</param>
    /// <param name="document">بيانات المستند</param>
    /// <param name="userId">معرف المستخدم</param>
    /// <returns>المستند المضاف</returns>
    Task<CarDocument> AddCarDocumentAsync(int carId, CarDocument document, int? userId = null);

    /// <summary>
    /// الحصول على صور السيارة
    /// </summary>
    /// <param name="carId">معرف السيارة</param>
    /// <returns>قائمة الصور</returns>
    Task<IEnumerable<CarImage>> GetCarImagesAsync(int carId);

    /// <summary>
    /// الحصول على مستندات السيارة
    /// </summary>
    /// <param name="carId">معرف السيارة</param>
    /// <returns>قائمة المستندات</returns>
    Task<IEnumerable<CarDocument>> GetCarDocumentsAsync(int carId);

    /// <summary>
    /// تعيين الصورة الرئيسية للسيارة
    /// </summary>
    /// <param name="carId">معرف السيارة</param>
    /// <param name="imageId">معرف الصورة</param>
    /// <param name="userId">معرف المستخدم</param>
    /// <returns>true إذا تم التعيين بنجاح</returns>
    Task<bool> SetPrimaryImageAsync(int carId, int imageId, int? userId = null);

    /// <summary>
    /// حذف صورة السيارة
    /// </summary>
    /// <param name="imageId">معرف الصورة</param>
    /// <param name="userId">معرف المستخدم</param>
    /// <returns>true إذا تم الحذف بنجاح</returns>
    Task<bool> DeleteCarImageAsync(int imageId, int? userId = null);

    /// <summary>
    /// حذف مستند السيارة
    /// </summary>
    /// <param name="documentId">معرف المستند</param>
    /// <param name="userId">معرف المستخدم</param>
    /// <returns>true إذا تم الحذف بنجاح</returns>
    Task<bool> DeleteCarDocumentAsync(int documentId, int? userId = null);

    /// <summary>
    /// إضافة سجل صيانة للسيارة
    /// </summary>
    /// <param name="carId">معرف السيارة</param>
    /// <param name="maintenanceRecord">سجل الصيانة</param>
    /// <param name="userId">معرف المستخدم</param>
    /// <returns>سجل الصيانة المضاف</returns>
    Task<MaintenanceRecord> AddMaintenanceRecordAsync(int carId, MaintenanceRecord maintenanceRecord, int? userId = null);

    /// <summary>
    /// الحصول على سجلات الصيانة للسيارة
    /// </summary>
    /// <param name="carId">معرف السيارة</param>
    /// <returns>قائمة سجلات الصيانة</returns>
    Task<IEnumerable<MaintenanceRecord>> GetMaintenanceRecordsAsync(int carId);

    /// <summary>
    /// الحصول على إحصائيات السيارات
    /// </summary>
    /// <returns>إحصائيات السيارات</returns>
    Task<CarStatistics> GetCarStatisticsAsync();

    /// <summary>
    /// الحصول على السيارات التي تحتاج صيانة
    /// </summary>
    /// <returns>قائمة السيارات التي تحتاج صيانة</returns>
    Task<IEnumerable<Car>> GetCarsNeedingMaintenanceAsync();

    /// <summary>
    /// الحصول على أفضل السيارات مبيعاً
    /// </summary>
    /// <param name="count">عدد السيارات</param>
    /// <returns>قائمة أفضل السيارات مبيعاً</returns>
    Task<IEnumerable<Car>> GetTopSellingCarsAsync(int count = 10);

    /// <summary>
    /// التحقق من توفر الرقم التسلسلي
    /// </summary>
    /// <param name="chassisNumber">الرقم التسلسلي</param>
    /// <param name="excludeCarId">معرف السيارة المستثناة (للتحديث)</param>
    /// <returns>true إذا كان الرقم متاح</returns>
    Task<bool> IsChassisNumberAvailableAsync(string chassisNumber, int? excludeCarId = null);
}

/// <summary>
/// إحصائيات السيارات
/// </summary>
public class CarStatistics
{
    /// <summary>
    /// العدد الإجمالي للسيارات
    /// </summary>
    public int TotalCars { get; set; }

    /// <summary>
    /// عدد السيارات المتاحة
    /// </summary>
    public int AvailableCars { get; set; }

    /// <summary>
    /// عدد السيارات المباعة
    /// </summary>
    public int SoldCars { get; set; }

    /// <summary>
    /// عدد السيارات المحجوزة
    /// </summary>
    public int ReservedCars { get; set; }

    /// <summary>
    /// عدد السيارات في الصيانة
    /// </summary>
    public int CarsInMaintenance { get; set; }

    /// <summary>
    /// القيمة الإجمالية للمخزون
    /// </summary>
    public decimal TotalInventoryValue { get; set; }

    /// <summary>
    /// متوسط سعر السيارة
    /// </summary>
    public decimal AverageCarPrice { get; set; }

    /// <summary>
    /// أعلى سعر سيارة
    /// </summary>
    public decimal HighestCarPrice { get; set; }

    /// <summary>
    /// أقل سعر سيارة
    /// </summary>
    public decimal LowestCarPrice { get; set; }

    /// <summary>
    /// عدد السيارات المضافة هذا الشهر
    /// </summary>
    public int CarsAddedThisMonth { get; set; }

    /// <summary>
    /// عدد السيارات المباعة هذا الشهر
    /// </summary>
    public int CarsSoldThisMonth { get; set; }
}
