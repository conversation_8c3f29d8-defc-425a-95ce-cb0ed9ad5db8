using Microsoft.EntityFrameworkCore;
using CarDealershipManagement.Enhanced.Data;
using CarDealershipManagement.Enhanced.Models;
using CarDealershipManagement.Enhanced.Enums;
using CarDealershipManagement.Enhanced.Services.Interfaces;

namespace CarDealershipManagement.Enhanced.Services;

/// <summary>
/// خدمة العملاء
/// </summary>
public class CustomerService : BaseService<Customer>, ICustomerService
{
    public CustomerService(CarDealershipDbContext context) : base(context)
    {
    }

    protected override IQueryable<Customer> ApplyIncludes(IQueryable<Customer> query)
    {
        return query
            .Include(c => c.SalesAgent)
            .Include(c => c.Sales)
            .Include(c => c.CustomerDocuments)
            .Include(c => c.CustomerInteractions);
    }

    public async Task<Customer?> GetByIdentityNumberAsync(string identityNumber)
    {
        return await ApplyIncludes(_dbSet)
            .FirstOrDefaultAsync(c => c.IdentityNumber == identityNumber);
    }

    public async Task<Customer?> GetByPhoneAsync(string phone)
    {
        return await ApplyIncludes(_dbSet)
            .FirstOrDefaultAsync(c => c.Phone == phone);
    }

    public async Task<Customer?> GetByEmailAsync(string email)
    {
        return await ApplyIncludes(_dbSet)
            .FirstOrDefaultAsync(c => c.Email == email);
    }

    public async Task<IEnumerable<Customer>> GetByStatusAsync(CustomerStatus status)
    {
        return await ApplyIncludes(_dbSet)
            .Where(c => c.Status == status)
            .ToListAsync();
    }

    public async Task<IEnumerable<Customer>> GetActiveCustomersAsync()
    {
        return await GetByStatusAsync(CustomerStatus.Active);
    }

    public async Task<IEnumerable<Customer>> GetVipCustomersAsync()
    {
        return await ApplyIncludes(_dbSet)
            .Where(c => c.IsVip)
            .ToListAsync();
    }

    public async Task<IEnumerable<Customer>> GetBySalesAgentAsync(int salesAgentId)
    {
        return await ApplyIncludes(_dbSet)
            .Where(c => c.SalesAgentId == salesAgentId)
            .ToListAsync();
    }

    public async Task<IEnumerable<Customer>> SearchCustomersAsync(
        string? searchTerm = null,
        CustomerStatus? status = null,
        string? city = null,
        int? salesAgentId = null,
        bool? isVip = null)
    {
        var query = ApplyIncludes(_dbSet).AsQueryable();

        if (!string.IsNullOrEmpty(searchTerm))
        {
            query = query.Where(c => 
                c.FirstName.Contains(searchTerm) ||
                c.LastName.Contains(searchTerm) ||
                c.Phone.Contains(searchTerm) ||
                c.Email.Contains(searchTerm) ||
                c.IdentityNumber.Contains(searchTerm));
        }

        if (status.HasValue)
            query = query.Where(c => c.Status == status.Value);

        if (!string.IsNullOrEmpty(city))
            query = query.Where(c => c.City.Contains(city));

        if (salesAgentId.HasValue)
            query = query.Where(c => c.SalesAgentId == salesAgentId.Value);

        if (isVip.HasValue)
            query = query.Where(c => c.IsVip == isVip.Value);

        return await query.ToListAsync();
    }

    public async Task<bool> UpdateCustomerStatusAsync(int customerId, CustomerStatus status, int? userId = null)
    {
        var customer = await GetByIdAsync(customerId);
        if (customer == null) return false;

        customer.Status = status;
        customer.UpdateModificationInfo(userId);

        await UpdateAsync(customer, userId);
        return true;
    }

    public async Task<bool> UpdateCreditLimitAsync(int customerId, decimal creditLimit, int? userId = null)
    {
        var customer = await GetByIdAsync(customerId);
        if (customer == null) return false;

        customer.CreditLimit = creditLimit;
        customer.UpdateModificationInfo(userId);

        await UpdateAsync(customer, userId);
        return true;
    }

    public async Task<bool> UpdateCustomerRatingAsync(int customerId, int rating, int? userId = null)
    {
        var customer = await GetByIdAsync(customerId);
        if (customer == null) return false;

        customer.Rating = Math.Max(1, Math.Min(5, rating)); // تأكد من أن التقييم بين 1 و 5
        customer.UpdateModificationInfo(userId);

        await UpdateAsync(customer, userId);
        return true;
    }

    public async Task<CustomerDocument> AddCustomerDocumentAsync(int customerId, CustomerDocument document, int? userId = null)
    {
        document.CustomerId = customerId;
        document.CreatedAt = DateTime.Now;
        document.CreatedBy = userId;

        _context.CustomerDocuments.Add(document);
        await _context.SaveChangesAsync();

        return document;
    }

    public async Task<IEnumerable<CustomerDocument>> GetCustomerDocumentsAsync(int customerId)
    {
        return await _context.CustomerDocuments
            .Where(d => d.CustomerId == customerId)
            .OrderBy(d => d.DocumentType)
            .ThenBy(d => d.CreatedAt)
            .ToListAsync();
    }

    public async Task<bool> DeleteCustomerDocumentAsync(int documentId, int? userId = null)
    {
        var document = await _context.CustomerDocuments.FindAsync(documentId);
        if (document == null) return false;

        document.SoftDelete(userId);
        await _context.SaveChangesAsync();
        return true;
    }

    public async Task<CustomerInteraction> AddCustomerInteractionAsync(int customerId, CustomerInteraction interaction, int? userId = null)
    {
        interaction.CustomerId = customerId;
        interaction.CreatedAt = DateTime.Now;
        interaction.CreatedBy = userId;

        _context.CustomerInteractions.Add(interaction);
        await _context.SaveChangesAsync();

        return interaction;
    }

    public async Task<IEnumerable<CustomerInteraction>> GetCustomerInteractionsAsync(int customerId)
    {
        return await _context.CustomerInteractions
            .Where(i => i.CustomerId == customerId)
            .OrderByDescending(i => i.InteractionDate)
            .ToListAsync();
    }

    public async Task<IEnumerable<Sale>> GetCustomerSalesAsync(int customerId)
    {
        return await _context.Sales
            .Include(s => s.Car)
            .Include(s => s.SalesAgent)
            .Where(s => s.CustomerId == customerId)
            .OrderByDescending(s => s.SaleDate)
            .ToListAsync();
    }

    public async Task<IEnumerable<InstallmentPayment>> GetCustomerInstallmentsAsync(int customerId)
    {
        return await _context.InstallmentPayments
            .Include(i => i.Sale)
            .ThenInclude(s => s.Car)
            .Where(i => i.Sale.CustomerId == customerId)
            .OrderBy(i => i.DueDate)
            .ToListAsync();
    }

    public async Task<IEnumerable<InstallmentPayment>> GetCustomerOverdueInstallmentsAsync(int customerId)
    {
        return await _context.InstallmentPayments
            .Include(i => i.Sale)
            .ThenInclude(s => s.Car)
            .Where(i => i.Sale.CustomerId == customerId && 
                       i.Status == InstallmentStatus.Overdue)
            .OrderBy(i => i.DueDate)
            .ToListAsync();
    }

    public async Task<decimal> CalculateCustomerTotalPurchasesAsync(int customerId)
    {
        return await _context.Sales
            .Where(s => s.CustomerId == customerId && s.Status == SaleStatus.Confirmed)
            .SumAsync(s => s.TotalAmount);
    }

    public async Task<decimal> CalculateCustomerCurrentBalanceAsync(int customerId)
    {
        var totalPurchases = await CalculateCustomerTotalPurchasesAsync(customerId);
        var totalPayments = await _context.InstallmentPayments
            .Where(i => i.Sale.CustomerId == customerId && i.Status == InstallmentStatus.Paid)
            .SumAsync(i => i.PaidAmount);

        return totalPurchases - totalPayments;
    }

    public async Task<CustomerStatistics> GetCustomerStatisticsAsync()
    {
        var totalCustomers = await _dbSet.CountAsync();
        var activeCustomers = await _dbSet.CountAsync(c => c.Status == CustomerStatus.Active);
        var vipCustomers = await _dbSet.CountAsync(c => c.IsVip);

        var thisMonth = DateTime.Now.Date.AddDays(-DateTime.Now.Day + 1);
        var newCustomersThisMonth = await _dbSet.CountAsync(c => c.CreatedAt >= thisMonth);

        var customersWithOverdue = await _context.InstallmentPayments
            .Where(i => i.Status == InstallmentStatus.Overdue)
            .Select(i => i.Sale.CustomerId)
            .Distinct()
            .CountAsync();

        var totalPurchases = await _context.Sales
            .Where(s => s.Status == SaleStatus.Confirmed)
            .SumAsync(s => s.TotalAmount);

        var averagePurchase = totalCustomers > 0 ? totalPurchases / totalCustomers : 0;
        var highestPurchase = await _context.Sales
            .Where(s => s.Status == SaleStatus.Confirmed)
            .GroupBy(s => s.CustomerId)
            .Select(g => g.Sum(s => s.TotalAmount))
            .DefaultIfEmpty(0)
            .MaxAsync();

        var outstandingBalance = await _context.InstallmentPayments
            .Where(i => i.Status != InstallmentStatus.Paid)
            .SumAsync(i => i.Amount - i.PaidAmount);

        var averageRating = await _dbSet
            .Where(c => c.Rating > 0)
            .AverageAsync(c => (double)c.Rating);

        return new CustomerStatistics
        {
            TotalCustomers = totalCustomers,
            ActiveCustomers = activeCustomers,
            VipCustomers = vipCustomers,
            NewCustomersThisMonth = newCustomersThisMonth,
            CustomersWithOverdueInstallments = customersWithOverdue,
            TotalCustomerPurchases = totalPurchases,
            AverageCustomerPurchase = averagePurchase,
            HighestCustomerPurchase = highestPurchase,
            TotalOutstandingBalance = outstandingBalance,
            AverageRating = averageRating
        };
    }

    public async Task<IEnumerable<Customer>> GetCustomersWithOverdueInstallmentsAsync()
    {
        return await _context.InstallmentPayments
            .Where(i => i.Status == InstallmentStatus.Overdue)
            .Select(i => i.Sale.Customer)
            .Distinct()
            .Include(c => c.SalesAgent)
            .ToListAsync();
    }

    public async Task<IEnumerable<Customer>> GetTopCustomersAsync(int count = 10)
    {
        return await _context.Sales
            .Where(s => s.Status == SaleStatus.Confirmed)
            .GroupBy(s => s.CustomerId)
            .OrderByDescending(g => g.Sum(s => s.TotalAmount))
            .Take(count)
            .Select(g => g.First().Customer)
            .Include(c => c.SalesAgent)
            .ToListAsync();
    }

    public async Task<bool> IsIdentityNumberAvailableAsync(string identityNumber, int? excludeCustomerId = null)
    {
        var query = _dbSet.Where(c => c.IdentityNumber == identityNumber);
        
        if (excludeCustomerId.HasValue)
            query = query.Where(c => c.Id != excludeCustomerId.Value);

        return !await query.AnyAsync();
    }

    public async Task<bool> IsPhoneAvailableAsync(string phone, int? excludeCustomerId = null)
    {
        var query = _dbSet.Where(c => c.Phone == phone);
        
        if (excludeCustomerId.HasValue)
            query = query.Where(c => c.Id != excludeCustomerId.Value);

        return !await query.AnyAsync();
    }

    public async Task<bool> IsEmailAvailableAsync(string email, int? excludeCustomerId = null)
    {
        var query = _dbSet.Where(c => c.Email == email);
        
        if (excludeCustomerId.HasValue)
            query = query.Where(c => c.Id != excludeCustomerId.Value);

        return !await query.AnyAsync();
    }

    protected override async Task<bool> ValidateEntityAsync(Customer entity)
    {
        // التحقق من عدم تكرار رقم الهوية
        if (!await IsIdentityNumberAvailableAsync(entity.IdentityNumber, entity.Id))
            return false;

        // التحقق من عدم تكرار رقم الهاتف
        if (!string.IsNullOrEmpty(entity.Phone) && !await IsPhoneAvailableAsync(entity.Phone, entity.Id))
            return false;

        // التحقق من عدم تكرار البريد الإلكتروني
        if (!string.IsNullOrEmpty(entity.Email) && !await IsEmailAvailableAsync(entity.Email, entity.Id))
            return false;

        // التحقق من صحة البيانات الأساسية
        if (string.IsNullOrEmpty(entity.FirstName) || 
            string.IsNullOrEmpty(entity.LastName) || 
            string.IsNullOrEmpty(entity.IdentityNumber))
            return false;

        return true;
    }

    protected override async Task OnAfterAddAsync(Customer entity, int? userId = null)
    {
        // إضافة تفاعل أولي مع العميل
        var interaction = new CustomerInteraction
        {
            CustomerId = entity.Id,
            InteractionType = "تسجيل عميل جديد",
            Description = "تم تسجيل العميل في النظام",
            InteractionDate = DateTime.Now,
            CreatedAt = DateTime.Now,
            CreatedBy = userId
        };

        await AddCustomerInteractionAsync(entity.Id, interaction, userId);
        await base.OnAfterAddAsync(entity, userId);
    }
}
