namespace CarDealershipManagement.Enhanced.Services.Interfaces;

using CarDealershipManagement.Enhanced.Models;
using CarDealershipManagement.Enhanced.Enums;

namespace CarDealershipManagement.Enhanced.Services.Interfaces;

/// <summary>
/// واجهة خدمة إدارة الصلاحيات
/// </summary>
public interface IPermissionService
{
    Task<bool> HasPermissionAsync(int userId, string permissionName);
    Task<IEnumerable<string>> GetUserPermissionsAsync(int userId);
    Task<bool> GrantPermissionAsync(int userId, string permissionName, int grantedBy);
    Task<bool> RevokePermissionAsync(int userId, string permissionName, int revokedBy);
    Task<IEnumerable<string>> GetAllPermissionsAsync();
    Task<bool> IsPermissionValidAsync(string permissionName);
    Task<IEnumerable<UserPermission>> GetPermissionsByRoleAsync(UserRole role);
}
