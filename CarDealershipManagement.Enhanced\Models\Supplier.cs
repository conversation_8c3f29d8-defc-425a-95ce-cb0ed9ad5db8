using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using CarDealershipManagement.Enhanced.Enums;

namespace CarDealershipManagement.Enhanced.Models;

/// <summary>
/// نموذج المورد
/// </summary>
[Table("Suppliers")]
public class Supplier : BaseEntity
{
    /// <summary>
    /// اسم الشركة
    /// </summary>
    [Required(ErrorMessage = "اسم الشركة مطلوب")]
    [StringLength(200, ErrorMessage = "اسم الشركة يجب أن يكون أقل من 200 حرف")]
    [Column("CompanyName")]
    public string CompanyName { get; set; } = string.Empty;

    /// <summary>
    /// اسم جهة الاتصال
    /// </summary>
    [Required(ErrorMessage = "اسم جهة الاتصال مطلوب")]
    [StringLength(200, ErrorMessage = "اسم جهة الاتصال يجب أن يكون أقل من 200 حرف")]
    [Column("ContactName")]
    public string ContactName { get; set; } = string.Empty;

    /// <summary>
    /// المنصب
    /// </summary>
    [StringLength(100)]
    [Column("ContactTitle")]
    public string? ContactTitle { get; set; }

    /// <summary>
    /// رقم الهاتف
    /// </summary>
    [Required(ErrorMessage = "رقم الهاتف مطلوب")]
    [StringLength(20, ErrorMessage = "رقم الهاتف يجب أن يكون أقل من 20 حرف")]
    [Phone(ErrorMessage = "رقم الهاتف غير صحيح")]
    [Column("Phone")]
    public string Phone { get; set; } = string.Empty;

    /// <summary>
    /// رقم هاتف إضافي
    /// </summary>
    [StringLength(20)]
    [Phone(ErrorMessage = "رقم الهاتف الإضافي غير صحيح")]
    [Column("AlternativePhone")]
    public string? AlternativePhone { get; set; }

    /// <summary>
    /// رقم الفاكس
    /// </summary>
    [StringLength(20)]
    [Column("Fax")]
    public string? Fax { get; set; }

    /// <summary>
    /// البريد الإلكتروني
    /// </summary>
    [StringLength(200)]
    [EmailAddress(ErrorMessage = "البريد الإلكتروني غير صحيح")]
    [Column("Email")]
    public string? Email { get; set; }

    /// <summary>
    /// الموقع الإلكتروني
    /// </summary>
    [StringLength(200)]
    [Url(ErrorMessage = "الموقع الإلكتروني غير صحيح")]
    [Column("Website")]
    public string? Website { get; set; }

    /// <summary>
    /// العنوان
    /// </summary>
    [Required(ErrorMessage = "العنوان مطلوب")]
    [StringLength(500, ErrorMessage = "العنوان يجب أن يكون أقل من 500 حرف")]
    [Column("Address")]
    public string Address { get; set; } = string.Empty;

    /// <summary>
    /// المدينة
    /// </summary>
    [Required(ErrorMessage = "المدينة مطلوبة")]
    [StringLength(100, ErrorMessage = "المدينة يجب أن تكون أقل من 100 حرف")]
    [Column("City")]
    public string City { get; set; } = string.Empty;

    /// <summary>
    /// المحافظة/المنطقة
    /// </summary>
    [StringLength(100)]
    [Column("State")]
    public string? State { get; set; }

    /// <summary>
    /// الرمز البريدي
    /// </summary>
    [StringLength(10)]
    [Column("PostalCode")]
    public string? PostalCode { get; set; }

    /// <summary>
    /// الدولة
    /// </summary>
    [StringLength(100)]
    [Column("Country")]
    public string? Country { get; set; } = "المملكة العربية السعودية";

    /// <summary>
    /// رقم السجل التجاري
    /// </summary>
    [StringLength(50)]
    [Column("CommercialRegister")]
    public string? CommercialRegister { get; set; }

    /// <summary>
    /// الرقم الضريبي
    /// </summary>
    [StringLength(50)]
    [Column("TaxNumber")]
    public string? TaxNumber { get; set; }

    /// <summary>
    /// نوع النشاط
    /// </summary>
    [StringLength(200)]
    [Column("BusinessType")]
    public string? BusinessType { get; set; }

    /// <summary>
    /// التخصص (أنواع السيارات)
    /// </summary>
    [StringLength(500)]
    [Column("Specialization")]
    public string? Specialization { get; set; }

    /// <summary>
    /// حالة المورد
    /// </summary>
    [Required]
    [Column("Status")]
    public SupplierStatus Status { get; set; } = SupplierStatus.Active;

    /// <summary>
    /// تقييم المورد (من 1 إلى 5)
    /// </summary>
    [Range(1, 5, ErrorMessage = "التقييم يجب أن يكون بين 1 و 5")]
    [Column("Rating")]
    public int? Rating { get; set; }

    /// <summary>
    /// شروط الدفع (بالأيام)
    /// </summary>
    [Range(0, 365, ErrorMessage = "شروط الدفع يجب أن تكون بين 0 و 365 يوم")]
    [Column("PaymentTerms")]
    public int PaymentTerms { get; set; } = 30;

    /// <summary>
    /// الحد الائتماني
    /// </summary>
    [Range(0, double.MaxValue, ErrorMessage = "الحد الائتماني يجب أن يكون أكبر من أو يساوي صفر")]
    [Column("CreditLimit", TypeName = "decimal(18,2)")]
    public decimal CreditLimit { get; set; } = 0;

    /// <summary>
    /// الرصيد الحالي (المستحق)
    /// </summary>
    [Column("CurrentBalance", TypeName = "decimal(18,2)")]
    public decimal CurrentBalance { get; set; } = 0;

    /// <summary>
    /// تاريخ بداية التعامل
    /// </summary>
    [Column("StartDate")]
    public DateTime? StartDate { get; set; }

    /// <summary>
    /// تاريخ آخر تعامل
    /// </summary>
    [Column("LastTransactionDate")]
    public DateTime? LastTransactionDate { get; set; }

    /// <summary>
    /// العملة المفضلة
    /// </summary>
    [StringLength(10)]
    [Column("PreferredCurrency")]
    public string? PreferredCurrency { get; set; } = "SAR";

    /// <summary>
    /// طريقة الدفع المفضلة
    /// </summary>
    [StringLength(50)]
    [Column("PreferredPaymentMethod")]
    public string? PreferredPaymentMethod { get; set; }

    /// <summary>
    /// معلومات البنك
    /// </summary>
    [StringLength(500)]
    [Column("BankDetails")]
    public string? BankDetails { get; set; }

    /// <summary>
    /// شروط وأحكام خاصة
    /// </summary>
    [StringLength(2000)]
    [Column("SpecialTerms")]
    public string? SpecialTerms { get; set; }

    /// <summary>
    /// ملاحظات
    /// </summary>
    [StringLength(2000)]
    [Column("SupplierNotes")]
    public string? SupplierNotes { get; set; }

    // Navigation Properties

    /// <summary>
    /// السيارات المشتراة من هذا المورد
    /// </summary>
    public virtual ICollection<Car> Cars { get; set; } = new List<Car>();

    /// <summary>
    /// مستندات المورد
    /// </summary>
    public virtual ICollection<SupplierDocument> SupplierDocuments { get; set; } = new List<SupplierDocument>();

    /// <summary>
    /// المدفوعات للمورد
    /// </summary>
    public virtual ICollection<SupplierPayment> SupplierPayments { get; set; } = new List<SupplierPayment>();

    /// <summary>
    /// العقود مع المورد
    /// </summary>
    public virtual ICollection<SupplierContract> SupplierContracts { get; set; } = new List<SupplierContract>();

    // Computed Properties

    /// <summary>
    /// إجمالي المشتريات من المورد
    /// </summary>
    [NotMapped]
    public decimal TotalPurchases => Cars?.Sum(c => c.PurchasePrice) ?? 0;

    /// <summary>
    /// عدد السيارات المشتراة
    /// </summary>
    [NotMapped]
    public int TotalCarsPurchased => Cars?.Count ?? 0;

    /// <summary>
    /// متوسط سعر السيارة
    /// </summary>
    [NotMapped]
    public decimal AverageCarPrice => TotalCarsPurchased > 0 ? TotalPurchases / TotalCarsPurchased : 0;

    /// <summary>
    /// الرصيد المتاح
    /// </summary>
    [NotMapped]
    public decimal AvailableCredit => CreditLimit - CurrentBalance;

    /// <summary>
    /// هل المورد مفضل
    /// </summary>
    [NotMapped]
    public bool IsPreferred => Status == SupplierStatus.Preferred;

    /// <summary>
    /// عدد أيام آخر معاملة
    /// </summary>
    [NotMapped]
    public int? DaysSinceLastTransaction => LastTransactionDate.HasValue ? (DateTime.Now - LastTransactionDate.Value).Days : null;

    /// <summary>
    /// هل المورد متأخر في الدفع
    /// </summary>
    [NotMapped]
    public bool IsOverdue => CurrentBalance > 0 && DaysSinceLastTransaction > PaymentTerms;
}
