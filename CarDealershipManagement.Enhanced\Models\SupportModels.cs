using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using CarDealershipManagement.Enhanced.Enums;

namespace CarDealershipManagement.Enhanced.Models;

/// <summary>
/// صلاحيات المستخدم
/// </summary>
[Table("UserPermissions")]
public class UserPermission : BaseEntity
{
    /// <summary>
    /// معرف المستخدم
    /// </summary>
    [Required(ErrorMessage = "المستخدم مطلوب")]
    [Column("UserId")]
    public int UserId { get; set; }

    /// <summary>
    /// اسم الصلاحية
    /// </summary>
    [Required(ErrorMessage = "اسم الصلاحية مطلوب")]
    [StringLength(100, ErrorMessage = "اسم الصلاحية يجب أن يكون أقل من 100 حرف")]
    [Column("PermissionName")]
    public string PermissionName { get; set; } = string.Empty;

    /// <summary>
    /// هل الصلاحية ممنوحة
    /// </summary>
    [Column("IsGranted")]
    public bool IsGranted { get; set; } = true;

    /// <summary>
    /// تاريخ انتهاء الصلاحية
    /// </summary>
    [Column("ExpiryDate")]
    public DateTime? ExpiryDate { get; set; }

    /// <summary>
    /// المستخدم
    /// </summary>
    [ForeignKey("UserId")]
    public virtual User User { get; set; } = null!;

    /// <summary>
    /// هل الصلاحية منتهية الصلاحية
    /// </summary>
    [NotMapped]
    public bool IsExpired => ExpiryDate.HasValue && ExpiryDate.Value < DateTime.Now;

    /// <summary>
    /// هل الصلاحية نشطة
    /// </summary>
    [NotMapped]
    public bool IsActive => IsGranted && !IsExpired && !IsDeleted;
}

/// <summary>
/// سجل تسجيل الدخول
/// </summary>
[Table("UserLoginLogs")]
public class UserLoginLog : BaseEntity
{
    /// <summary>
    /// معرف المستخدم
    /// </summary>
    [Required(ErrorMessage = "المستخدم مطلوب")]
    [Column("UserId")]
    public int UserId { get; set; }

    /// <summary>
    /// تاريخ تسجيل الدخول
    /// </summary>
    [Required]
    [Column("LoginDate")]
    public DateTime LoginDate { get; set; } = DateTime.Now;

    /// <summary>
    /// تاريخ تسجيل الخروج
    /// </summary>
    [Column("LogoutDate")]
    public DateTime? LogoutDate { get; set; }

    /// <summary>
    /// عنوان IP
    /// </summary>
    [StringLength(45)] // IPv6 support
    [Column("IpAddress")]
    public string? IpAddress { get; set; }

    /// <summary>
    /// معلومات المتصفح
    /// </summary>
    [StringLength(500)]
    [Column("UserAgent")]
    public string? UserAgent { get; set; }

    /// <summary>
    /// هل تسجيل الدخول ناجح
    /// </summary>
    [Column("IsSuccessful")]
    public bool IsSuccessful { get; set; } = true;

    /// <summary>
    /// سبب فشل تسجيل الدخول
    /// </summary>
    [StringLength(500)]
    [Column("FailureReason")]
    public string? FailureReason { get; set; }

    /// <summary>
    /// المستخدم
    /// </summary>
    [ForeignKey("UserId")]
    public virtual User User { get; set; } = null!;

    /// <summary>
    /// مدة الجلسة
    /// </summary>
    [NotMapped]
    public TimeSpan? SessionDuration => LogoutDate.HasValue ? LogoutDate.Value - LoginDate : null;
}

/// <summary>
/// سجل التدقيق
/// </summary>
[Table("AuditLogs")]
public class AuditLog : BaseEntity
{
    /// <summary>
    /// معرف المستخدم
    /// </summary>
    [Column("UserId")]
    public int? UserId { get; set; }

    /// <summary>
    /// نوع العملية
    /// </summary>
    [Required]
    [Column("Action")]
    public AuditAction Action { get; set; }

    /// <summary>
    /// اسم الجدول المتأثر
    /// </summary>
    [Required(ErrorMessage = "اسم الجدول مطلوب")]
    [StringLength(100, ErrorMessage = "اسم الجدول يجب أن يكون أقل من 100 حرف")]
    [Column("TableName")]
    public string TableName { get; set; } = string.Empty;

    /// <summary>
    /// معرف السجل المتأثر
    /// </summary>
    [Column("RecordId")]
    public int? RecordId { get; set; }

    /// <summary>
    /// القيم القديمة (JSON)
    /// </summary>
    [Column("OldValues")]
    public string? OldValues { get; set; }

    /// <summary>
    /// القيم الجديدة (JSON)
    /// </summary>
    [Column("NewValues")]
    public string? NewValues { get; set; }

    /// <summary>
    /// تاريخ العملية
    /// </summary>
    [Required]
    [Column("ActionDate")]
    public DateTime ActionDate { get; set; } = DateTime.Now;

    /// <summary>
    /// عنوان IP
    /// </summary>
    [StringLength(45)]
    [Column("IpAddress")]
    public string? IpAddress { get; set; }

    /// <summary>
    /// معلومات إضافية
    /// </summary>
    [StringLength(1000)]
    [Column("AdditionalInfo")]
    public string? AdditionalInfo { get; set; }

    /// <summary>
    /// المستخدم
    /// </summary>
    [ForeignKey("UserId")]
    public virtual User? User { get; set; }
}

/// <summary>
/// إعدادات النظام
/// </summary>
[Table("SystemSettings")]
public class SystemSetting : BaseEntity
{
    /// <summary>
    /// مفتاح الإعداد
    /// </summary>
    [Required(ErrorMessage = "مفتاح الإعداد مطلوب")]
    [StringLength(100, ErrorMessage = "مفتاح الإعداد يجب أن يكون أقل من 100 حرف")]
    [Column("SettingKey")]
    public string SettingKey { get; set; } = string.Empty;

    /// <summary>
    /// قيمة الإعداد
    /// </summary>
    [StringLength(2000)]
    [Column("SettingValue")]
    public string? SettingValue { get; set; }

    /// <summary>
    /// وصف الإعداد
    /// </summary>
    [StringLength(500)]
    [Column("Description")]
    public string? Description { get; set; }

    /// <summary>
    /// نوع البيانات
    /// </summary>
    [StringLength(50)]
    [Column("DataType")]
    public string? DataType { get; set; } = "string";

    /// <summary>
    /// هل الإعداد مشفر
    /// </summary>
    [Column("IsEncrypted")]
    public bool IsEncrypted { get; set; } = false;

    /// <summary>
    /// هل الإعداد للقراءة فقط
    /// </summary>
    [Column("IsReadOnly")]
    public bool IsReadOnly { get; set; } = false;

    /// <summary>
    /// المجموعة
    /// </summary>
    [StringLength(100)]
    [Column("Category")]
    public string? Category { get; set; }

    /// <summary>
    /// ترتيب العرض
    /// </summary>
    [Column("SortOrder")]
    public int SortOrder { get; set; } = 0;
}

/// <summary>
/// التراخيص المعتمدة
/// </summary>
[Table("AuthorizedInstallations")]
public class AuthorizedInstallation : BaseEntity
{
    /// <summary>
    /// معرف الجهاز
    /// </summary>
    [Required(ErrorMessage = "معرف الجهاز مطلوب")]
    [StringLength(100, ErrorMessage = "معرف الجهاز يجب أن يكون أقل من 100 حرف")]
    [Column("MachineId")]
    public string MachineId { get; set; } = string.Empty;

    /// <summary>
    /// اسم الجهاز
    /// </summary>
    [StringLength(200)]
    [Column("MachineName")]
    public string? MachineName { get; set; }

    /// <summary>
    /// نوع الترخيص
    /// </summary>
    [Required]
    [Column("LicenseType")]
    public LicenseType LicenseType { get; set; } = LicenseType.Trial;

    /// <summary>
    /// حالة الترخيص
    /// </summary>
    [Required]
    [Column("Status")]
    public LicenseStatus Status { get; set; } = LicenseStatus.Active;

    /// <summary>
    /// تاريخ بداية الترخيص
    /// </summary>
    [Required]
    [Column("StartDate")]
    public DateTime StartDate { get; set; } = DateTime.Now;

    /// <summary>
    /// تاريخ انتهاء الترخيص
    /// </summary>
    [Column("ExpiryDate")]
    public DateTime? ExpiryDate { get; set; }

    /// <summary>
    /// آخر تاريخ تحقق
    /// </summary>
    [Column("LastCheckDate")]
    public DateTime? LastCheckDate { get; set; }

    /// <summary>
    /// معلومات إضافية
    /// </summary>
    [StringLength(1000)]
    [Column("AdditionalInfo")]
    public string? AdditionalInfo { get; set; }

    /// <summary>
    /// هل الترخيص منتهي الصلاحية
    /// </summary>
    [NotMapped]
    public bool IsExpired => ExpiryDate.HasValue && ExpiryDate.Value < DateTime.Now;

    /// <summary>
    /// عدد أيام حتى انتهاء الصلاحية
    /// </summary>
    [NotMapped]
    public int? DaysUntilExpiry => ExpiryDate.HasValue ? (ExpiryDate.Value - DateTime.Now).Days : null;

    /// <summary>
    /// هل الترخيص نشط
    /// </summary>
    [NotMapped]
    public bool IsActive => Status == LicenseStatus.Active && !IsExpired;
}

/// <summary>
/// مدفوعات المورد
/// </summary>
[Table("SupplierPayments")]
public class SupplierPayment : BaseEntity
{
    /// <summary>
    /// معرف المورد
    /// </summary>
    [Required(ErrorMessage = "المورد مطلوب")]
    [Column("SupplierId")]
    public int SupplierId { get; set; }

    /// <summary>
    /// رقم الدفع
    /// </summary>
    [Required(ErrorMessage = "رقم الدفع مطلوب")]
    [StringLength(50, ErrorMessage = "رقم الدفع يجب أن يكون أقل من 50 حرف")]
    [Column("PaymentNumber")]
    public string PaymentNumber { get; set; } = string.Empty;

    /// <summary>
    /// المبلغ
    /// </summary>
    [Required(ErrorMessage = "المبلغ مطلوب")]
    [Range(0, double.MaxValue, ErrorMessage = "المبلغ يجب أن يكون أكبر من صفر")]
    [Column("Amount", TypeName = "decimal(18,2)")]
    public decimal Amount { get; set; }

    /// <summary>
    /// تاريخ الدفع
    /// </summary>
    [Required]
    [Column("PaymentDate")]
    public DateTime PaymentDate { get; set; } = DateTime.Now;

    /// <summary>
    /// نوع الدفع
    /// </summary>
    [Required]
    [Column("PaymentType")]
    public PaymentType PaymentType { get; set; } = PaymentType.Cash;

    /// <summary>
    /// رقم المرجع
    /// </summary>
    [StringLength(100)]
    [Column("ReferenceNumber")]
    public string? ReferenceNumber { get; set; }

    /// <summary>
    /// ملاحظات
    /// </summary>
    [StringLength(1000)]
    [Column("Notes")]
    public string? Notes { get; set; }

    /// <summary>
    /// المورد
    /// </summary>
    [ForeignKey("SupplierId")]
    public virtual Supplier Supplier { get; set; } = null!;
}
