using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;
using CarDealershipManagement.Enhanced.Services.Interfaces;
using CarDealershipManagement.Enhanced.Models;
using CarDealershipManagement.Enhanced.Enums;

namespace CarDealershipManagement.Enhanced.Forms;

/// <summary>
/// النموذج الرئيسي للتطبيق
/// </summary>
public partial class MainForm : Form
{
    private readonly IServiceProvider _serviceProvider;
    private readonly ILogger<MainForm> _logger;
    private readonly IUserService _userService;
    private readonly IPermissionService _permissionService;
    
    private User? _currentUser;
    private readonly Dictionary<string, Form> _openForms = new();

    public MainForm(IServiceProvider serviceProvider)
    {
        _serviceProvider = serviceProvider;
        _logger = serviceProvider.GetRequiredService<ILogger<MainForm>>();
        _userService = serviceProvider.GetRequiredService<IUserService>();
        _permissionService = serviceProvider.GetRequiredService<IPermissionService>();

        InitializeComponent();
        InitializeApplication();
    }

    /// <summary>
    /// تهيئة التطبيق
    /// </summary>
    private async void InitializeApplication()
    {
        try
        {
            // عرض نموذج تسجيل الدخول
            await ShowLoginFormAsync();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "خطأ في تهيئة التطبيق");
            MessageBox.Show($"خطأ في تهيئة التطبيق:\n{ex.Message}", "خطأ", 
                MessageBoxButtons.OK, MessageBoxIcon.Error);
        }
    }

    /// <summary>
    /// عرض نموذج تسجيل الدخول
    /// </summary>
    private async Task ShowLoginFormAsync()
    {
        var loginForm = new LoginForm(_serviceProvider);
        var result = loginForm.ShowDialog();

        if (result == DialogResult.OK && loginForm.CurrentUser != null)
        {
            _currentUser = loginForm.CurrentUser;
            await SetupMainFormAsync();
        }
        else
        {
            Application.Exit();
        }
    }

    /// <summary>
    /// إعداد النموذج الرئيسي
    /// </summary>
    private async Task SetupMainFormAsync()
    {
        if (_currentUser == null) return;

        // تحديث عنوان النافذة
        this.Text = $"نظام إدارة معارض السيارات - مرحباً {_currentUser.FullName}";

        // إعداد شريط الحالة
        await SetupStatusBarAsync();

        // إعداد القوائم حسب الصلاحيات
        await SetupMenusAsync();

        // تسجيل دخول المستخدم
        await _userService.LogUserLoginAsync(_currentUser.Id, Environment.MachineName);

        _logger.LogInformation("تم تسجيل دخول المستخدم: {Username}", _currentUser.Username);
    }

    /// <summary>
    /// إعداد شريط الحالة
    /// </summary>
    private async Task SetupStatusBarAsync()
    {
        statusStrip.Items.Clear();

        // معلومات المستخدم
        var userLabel = new ToolStripStatusLabel($"المستخدم: {_currentUser?.FullName}");
        statusStrip.Items.Add(userLabel);

        // الوقت والتاريخ
        var dateTimeLabel = new ToolStripStatusLabel(DateTime.Now.ToString("yyyy/MM/dd HH:mm"));
        statusStrip.Items.Add(dateTimeLabel);

        // حالة قاعدة البيانات
        var dbStatusLabel = new ToolStripStatusLabel("قاعدة البيانات: متصلة");
        dbStatusLabel.ForeColor = Color.Green;
        statusStrip.Items.Add(dbStatusLabel);

        await Task.CompletedTask;
    }

    /// <summary>
    /// إعداد القوائم حسب الصلاحيات
    /// </summary>
    private async Task SetupMenusAsync()
    {
        if (_currentUser == null) return;

        var userPermissions = await _permissionService.GetUserPermissionsAsync(_currentUser.Id);
        var permissions = userPermissions.ToHashSet();

        // قائمة السيارات
        carsToolStripMenuItem.Visible = permissions.Contains("Cars.View");
        
        // قائمة العملاء
        customersToolStripMenuItem.Visible = permissions.Contains("Customers.View");
        
        // قائمة الموردين
        suppliersToolStripMenuItem.Visible = permissions.Contains("Suppliers.View");
        
        // قائمة المبيعات
        salesToolStripMenuItem.Visible = permissions.Contains("Sales.View");
        
        // قائمة الأقساط
        installmentsToolStripMenuItem.Visible = permissions.Contains("Installments.View");
        
        // قائمة التقارير
        reportsToolStripMenuItem.Visible = permissions.Contains("Reports.View");
        
        // قائمة المستخدمين (للمديرين فقط)
        usersToolStripMenuItem.Visible = permissions.Contains("Users.View");
        
        // قائمة النظام
        systemToolStripMenuItem.Visible = permissions.Contains("System.Settings") || 
                                         permissions.Contains("System.Backup");
    }

    /// <summary>
    /// فتح نموذج فرعي
    /// </summary>
    private void OpenChildForm<T>(string formKey) where T : Form
    {
        try
        {
            if (_openForms.ContainsKey(formKey))
            {
                _openForms[formKey].BringToFront();
                return;
            }

            var form = (T)Activator.CreateInstance(typeof(T), _serviceProvider)!;
            form.MdiParent = this;
            form.FormClosed += (s, e) => _openForms.Remove(formKey);
            
            _openForms[formKey] = form;
            form.Show();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "خطأ في فتح النموذج: {FormType}", typeof(T).Name);
            MessageBox.Show($"خطأ في فتح النموذج:\n{ex.Message}", "خطأ", 
                MessageBoxButtons.OK, MessageBoxIcon.Error);
        }
    }

    /// <summary>
    /// تسجيل خروج المستخدم
    /// </summary>
    private async void LogoutUser()
    {
        try
        {
            if (_currentUser != null)
            {
                await _userService.LogUserLogoutAsync(_currentUser.Id);
                _logger.LogInformation("تم تسجيل خروج المستخدم: {Username}", _currentUser.Username);
            }

            // إغلاق جميع النماذج المفتوحة
            foreach (var form in _openForms.Values.ToList())
            {
                form.Close();
            }
            _openForms.Clear();

            // إعادة عرض نموذج تسجيل الدخول
            _currentUser = null;
            await ShowLoginFormAsync();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "خطأ في تسجيل الخروج");
            MessageBox.Show($"خطأ في تسجيل الخروج:\n{ex.Message}", "خطأ", 
                MessageBoxButtons.OK, MessageBoxIcon.Error);
        }
    }

    /// <summary>
    /// إغلاق التطبيق
    /// </summary>
    private async void ExitApplication()
    {
        try
        {
            if (_currentUser != null)
            {
                await _userService.LogUserLogoutAsync(_currentUser.Id);
            }

            Application.Exit();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "خطأ في إغلاق التطبيق");
            Application.Exit();
        }
    }

    #region Event Handlers

    private void carsToolStripMenuItem_Click(object sender, EventArgs e)
    {
        OpenChildForm<CarsForm>("Cars");
    }

    private void customersToolStripMenuItem_Click(object sender, EventArgs e)
    {
        OpenChildForm<CustomersForm>("Customers");
    }

    private void suppliersToolStripMenuItem_Click(object sender, EventArgs e)
    {
        OpenChildForm<SuppliersForm>("Suppliers");
    }

    private void salesToolStripMenuItem_Click(object sender, EventArgs e)
    {
        OpenChildForm<SalesForm>("Sales");
    }

    private void installmentsToolStripMenuItem_Click(object sender, EventArgs e)
    {
        OpenChildForm<InstallmentsForm>("Installments");
    }

    private void reportsToolStripMenuItem_Click(object sender, EventArgs e)
    {
        OpenChildForm<ReportsForm>("Reports");
    }

    private void usersToolStripMenuItem_Click(object sender, EventArgs e)
    {
        OpenChildForm<UsersForm>("Users");
    }

    private void logoutToolStripMenuItem_Click(object sender, EventArgs e)
    {
        LogoutUser();
    }

    private void exitToolStripMenuItem_Click(object sender, EventArgs e)
    {
        ExitApplication();
    }

    private async void MainForm_FormClosing(object sender, FormClosingEventArgs e)
    {
        if (_currentUser != null)
        {
            await _userService.LogUserLogoutAsync(_currentUser.Id);
        }
    }

    #endregion

    /// <summary>
    /// المستخدم الحالي
    /// </summary>
    public User? CurrentUser => _currentUser;
}
